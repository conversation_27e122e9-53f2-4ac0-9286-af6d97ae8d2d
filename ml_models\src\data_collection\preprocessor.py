"""Data preprocessing utilities for behavioral biometrics."""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import joblib
import os

class BehavioralFeatureExtractor:
    """Extract and preprocess behavioral biometric features."""
    
    def __init__(self):
        """Initialize preprocessors."""
        self.standard_scaler = StandardScaler()
        self.minmax_scaler = MinMaxScaler()
        self.feature_columns = []
        self.common_digraphs = [
            'th', 'he', 'in', 'er', 'an', 're', 'on', 'at', 'en', 'nd',
            'ti', 'es', 'or', 'te', 'of', 'ed', 'is', 'it', 'al', 'ar',
            'st', 'to', 'nt', 'ng', 'se', 'ha', 'as', 'ou', 'io', 'le',
            've', 'co', 'me', 'de', 'hi', 'ri', 'ro', 'ic', 'ne', 'ea'
        ]
        self.common_trigraphs = [
            'the', 'and', 'ing', 'ion', 'tio', 'ent', 'ati', 'for', 'her', 'ter',
            'hat', 'tha', 'ere', 'con', 'res', 'ver', 'all', 'ons', 'nce', 'men'
        ]
    
    def extract_typing_features(
        self, typing_data: List[Dict]
    ) -> pd.DataFrame:
        """Extract features from typing behavior.
        
        Args:
            typing_data: List of typing events with timestamps and keys
        
        Returns:
            DataFrame with extracted features
        """
        features = []
        
        for session in typing_data:
            events = pd.DataFrame(session)
            
            # Basic timing features
            keyhold_times = events['keyup_time'] - events['keydown_time']
            flight_times = events['keydown_time'].diff()
            
            # Compute statistics
            features_dict = {
                'mean_keyhold_time': keyhold_times.mean(),
                'std_keyhold_time': keyhold_times.std(),
                'mean_flight_time': flight_times.mean(),
                'std_flight_time': flight_times.std(),
                'typing_speed': len(events) / (
                    events['keyup_time'].max() - events['keydown_time'].min()
                ),
                'error_rate': events['is_error'].mean(),
                'backspace_rate': (events['key'] == 'Backspace').mean(),
                'special_key_rate': events['is_special_key'].mean()
            }
            
            # N-graph features (for common combinations)
            for n in [2, 3]:
                for combo in self._get_common_combinations(n):
                    feat_name = f"{combo}_time"
                    feat_value = self._compute_ngraph_time(events, combo)
                    features_dict[feat_name] = feat_value
            
            features.append(features_dict)
        
        return pd.DataFrame(features)
    
    def extract_touch_features(
        self, touch_data: List[Dict]
    ) -> pd.DataFrame:
        """Extract features from touch behavior.
        
        Args:
            touch_data: List of touch events with coordinates and timing
        
        Returns:
            DataFrame with extracted features
        """
        features = []
        
        for session in touch_data:
            events = pd.DataFrame(session)
            
            # Movement features
            dx = events['x'].diff()
            dy = events['y'].diff()
            velocities = np.sqrt(dx**2 + dy**2) / events['timestamp'].diff()
            accelerations = velocities.diff() / events['timestamp'].diff()
            
            # Pressure and size features
            if 'pressure' in events.columns:
                pressure_stats = events['pressure'].describe()
                size_stats = events['size'].describe()
            else:
                pressure_stats = pd.Series({
                    'mean': 0.5, 'std': 0.1,
                    'min': 0.3, 'max': 0.7
                })
                size_stats = pressure_stats.copy()
            
            features_dict = {
                # Movement features
                'mean_velocity': velocities.mean(),
                'std_velocity': velocities.std(),
                'max_velocity': velocities.max(),
                'mean_acceleration': accelerations.mean(),
                'std_acceleration': accelerations.std(),
                
                # Pressure features
                'mean_pressure': pressure_stats['mean'],
                'std_pressure': pressure_stats['std'],
                'min_pressure': pressure_stats['min'],
                'max_pressure': pressure_stats['max'],
                
                # Size features
                'mean_touch_size': size_stats['mean'],
                'std_touch_size': size_stats['std'],
                'min_touch_size': size_stats['min'],
                'max_touch_size': size_stats['max'],
                
                # Path features
                'total_distance': np.sqrt(dx**2 + dy**2).sum(),
                'path_efficiency': np.sqrt(
                    (events['x'].iloc[-1] - events['x'].iloc[0])**2 +
                    (events['y'].iloc[-1] - events['y'].iloc[0])**2
                ) / np.sqrt(dx**2 + dy**2).sum(),
                
                # Gesture features
                'gesture_duration': events['timestamp'].max() - events['timestamp'].min(),
                'num_touch_points': len(events),
                'touch_density': len(events) / (
                    events['timestamp'].max() - events['timestamp'].min()
                )
            }
            
            features.append(features_dict)
        
        return pd.DataFrame(features)
    
    def combine_and_normalize_features(
        self,
        typing_features: pd.DataFrame,
        touch_features: pd.DataFrame,
        fit: bool = False
    ) -> np.ndarray:
        """Combine and normalize features.
        
        Args:
            typing_features: DataFrame with typing features
            touch_features: DataFrame with touch features
            fit: Whether to fit the scalers (True for training)
        
        Returns:
            Normalized feature array
        """
        # Combine features
        combined = pd.concat([typing_features, touch_features], axis=1)
        
        if fit:
            self.feature_columns = combined.columns.tolist()
        else:
            # Ensure columns match training data
            combined = combined.reindex(columns=self.feature_columns, fill_value=0)
        
        # Apply standard scaling to timing features
        timing_features = [col for col in combined.columns if 'time' in col]
        if fit:
            timing_scaled = self.standard_scaler.fit_transform(
                combined[timing_features]
            )
        else:
            timing_scaled = self.standard_scaler.transform(
                combined[timing_features]
            )
        
        # Apply minmax scaling to other features
        other_features = [col for col in combined.columns if 'time' not in col]
        if fit:
            other_scaled = self.minmax_scaler.fit_transform(
                combined[other_features]
            )
        else:
            other_scaled = self.minmax_scaler.transform(
                combined[other_features]
            )
        
        # Combine scaled features
        scaled_features = np.hstack([timing_scaled, other_scaled])
        return scaled_features
    
    def fit(self, X: pd.DataFrame):
        """Fit preprocessors on training data."""
        self.feature_columns = X.columns.tolist()
        
        # Fit scalers
        self.standard_scaler.fit(X)
        self.minmax_scaler.fit(X)
    
    def transform(self, X: pd.DataFrame) -> np.ndarray:
        """Transform features for model input."""
        if not isinstance(X, pd.DataFrame):
            X = pd.DataFrame(X, columns=self.feature_columns)
        
        # First standardize
        X_scaled = self.standard_scaler.transform(X)
        
        # Then scale to [0,1] range
        X_final = self.minmax_scaler.transform(X_scaled)
        
        return X_final
    
    def fit_transform(self, X: pd.DataFrame) -> np.ndarray:
        """Fit and transform features."""
        self.fit(X)
        return self.transform(X)
    
    def save_preprocessor(self, save_dir: str):
        """Save preprocessor state."""
        os.makedirs(save_dir, exist_ok=True)
        
        joblib.dump(self.standard_scaler, os.path.join(save_dir, 'standard_scaler.pkl'))
        joblib.dump(self.minmax_scaler, os.path.join(save_dir, 'minmax_scaler.pkl'))
        joblib.dump(self.feature_columns, os.path.join(save_dir, 'feature_columns.pkl'))
        joblib.dump(self.common_digraphs, os.path.join(save_dir, 'common_digraphs.pkl'))
        joblib.dump(self.common_trigraphs, os.path.join(save_dir, 'common_trigraphs.pkl'))
    
    def load_preprocessor(self, save_dir: str):
        """Load preprocessor state."""
        self.standard_scaler = joblib.load(os.path.join(save_dir, 'standard_scaler.pkl'))
        self.minmax_scaler = joblib.load(os.path.join(save_dir, 'minmax_scaler.pkl'))
        self.feature_columns = joblib.load(os.path.join(save_dir, 'feature_columns.pkl'))
        self.common_digraphs = joblib.load(os.path.join(save_dir, 'common_digraphs.pkl'))
        self.common_trigraphs = joblib.load(os.path.join(save_dir, 'common_trigraphs.pkl'))

    def _get_common_combinations(self, n: int) -> List[str]:
        """Get common n-graphs for feature extraction."""
        if n == 2:
            return self.common_digraphs
        elif n == 3:
            return self.common_trigraphs
        return []
    
    def _compute_ngraph_time(self, events: pd.DataFrame, combo: str) -> float:
        """Compute timing for a specific n-graph combination."""
        combo_events = []
        for i in range(len(events) - len(combo) + 1):
            keys = ''.join(events['key'].iloc[i:i+len(combo)].astype(str))
            if keys.lower() == combo:
                start_time = events['keydown_time'].iloc[i]
                end_time = events['keyup_time'].iloc[i+len(combo)-1]
                combo_events.append(end_time - start_time)
        
        return np.mean(combo_events) if combo_events else 0.0
