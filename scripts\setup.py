#!/usr/bin/env python3
"""
TrustChain-Auth Setup Script
Automates the initial setup of the development environment
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True
        )
        if result.returncode != 0:
            print(f"Error running command: {command}")
            print(f"Error output: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"Exception running command {command}: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_flutter():
    """Check if Flutter is installed"""
    try:
        result = subprocess.run(
            ["flutter", "--version"], 
            capture_output=True, 
            text=True
        )
        if result.returncode == 0:
            print("✓ Flutter is installed")
            return True
        else:
            print("✗ Flutter is not installed or not in PATH")
            return False
    except FileNotFoundError:
        print("✗ Flutter is not installed or not in PATH")
        return False

def setup_python_environment():
    """Set up Python virtual environment and install dependencies"""
    print("\n🐍 Setting up Python environment...")
    
    # Create virtual environment if it doesn't exist
    if not os.path.exists("venv"):
        print("Creating virtual environment...")
        if not run_command("python -m venv venv"):
            return False
    
    # Determine activation script based on OS
    if platform.system() == "Windows":
        activate_script = "venv\\Scripts\\activate"
        pip_command = "venv\\Scripts\\pip"
    else:
        activate_script = "source venv/bin/activate"
        pip_command = "venv/bin/pip"
    
    # Install requirements
    print("Installing Python dependencies...")
    if not run_command(f"{pip_command} install -r requirements.txt"):
        return False
    
    print("✓ Python environment setup complete")
    return True

def setup_flutter_project():
    """Initialize Flutter project"""
    print("\n📱 Setting up Flutter project...")
    
    mobile_app_dir = Path("mobile_app")
    
    if not mobile_app_dir.exists():
        print("Creating Flutter project...")
        if not run_command("flutter create mobile_app"):
            return False
    else:
        print("Flutter project directory already exists")
    
    # Get Flutter dependencies
    print("Getting Flutter dependencies...")
    if not run_command("flutter pub get", cwd="mobile_app"):
        return False
    
    print("✓ Flutter project setup complete")
    return True

def create_config_files():
    """Create necessary configuration files"""
    print("\n⚙️ Creating configuration files...")
    
    # Copy example env file
    env_example = Path("config/development/.env.example")
    env_file = Path("config/development/.env")
    
    if env_example.exists() and not env_file.exists():
        import shutil
        shutil.copy(env_example, env_file)
        print("✓ Created development .env file")
    
    return True

def main():
    """Main setup function"""
    print("🔐 TrustChain-Auth Development Environment Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    flutter_available = check_flutter()
    if not flutter_available:
        print("\n⚠️  Flutter is not installed. Please install Flutter SDK first:")
        print("   https://docs.flutter.dev/get-started/install")
        print("   After installation, add Flutter to your PATH and run this script again.")
    
    # Setup Python environment
    if not setup_python_environment():
        print("❌ Failed to setup Python environment")
        sys.exit(1)
    
    # Setup Flutter project (only if Flutter is available)
    if flutter_available:
        if not setup_flutter_project():
            print("❌ Failed to setup Flutter project")
            sys.exit(1)
    
    # Create config files
    if not create_config_files():
        print("❌ Failed to create configuration files")
        sys.exit(1)
    
    print("\n🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Activate Python environment:")
    if platform.system() == "Windows":
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    
    if flutter_available:
        print("2. Navigate to mobile_app and run:")
        print("   cd mobile_app")
        print("   flutter run")
    else:
        print("2. Install Flutter SDK and run this script again")
    
    print("3. Start developing! 🚀")

if __name__ == "__main__":
    main()
