plugins {
    id "com.android.application"
    id "org.jetbrains.kotlin.android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    apply plugin: "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace "com.trustchain.auth.trustchain_auth"
    compileSdk 34
    ndkVersion "25.2.9519653"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    // Disable NDK completely
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.trustchain.auth.trustchain_auth"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk 21
        targetSdk 35
        versionCode 1
        versionName "1.0.0"

        // Completely disable NDK to avoid build issues
        ndk {
            abi<PERSON><PERSON><PERSON> 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
        }
    }
}

flutter {
    source "../.."
}
