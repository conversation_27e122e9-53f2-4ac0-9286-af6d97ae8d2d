"""Database models package."""

from ..base_class import Base
from .behavioral import BehavioralEvent, BehavioralFeature, BehavioralProfile, RiskAssessment
from .users import User
from .devices import Device
from .auth import AuthEvent, SecurityAlert
from .enums import UserRole, DeviceType, Platform, EventType

# For type annotations
__all__ = [
    'Base',
    # Core Models
    "User",
    "Device",
    "AuthEvent",
    "SecurityAlert",
    
    # Behavioral Models
    "BehavioralEvent",
    "BehavioralFeature", 
    "BehavioralProfile",
    "RiskAssessment",
    
    # Enums
    "UserRole",
    "DeviceType",
    "Platform",
    "EventType"
]
