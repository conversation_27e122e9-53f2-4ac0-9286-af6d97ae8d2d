"""
Simple test for Behavioral Autoencoder
"""

import numpy as np
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import only what we have
from models.autoencoder import BehavioralAutoencoder, AutoencoderTrainer, AutoencoderPredictor


def test_basic_functionality():
    """Test basic autoencoder functionality"""
    print("Testing Behavioral Autoencoder...")
    
    # Test parameters
    input_dim = 10
    n_samples = 50
    
    # Generate synthetic behavioral data
    np.random.seed(42)
    X_train = np.random.normal(0, 1, (n_samples, input_dim))
    X_test = np.random.normal(0, 1, (10, input_dim))
    
    print(f"Training data shape: {X_train.shape}")
    print(f"Test data shape: {X_test.shape}")
    
    # Create autoencoder
    autoencoder = BehavioralAutoencoder(
        input_dim=input_dim,
        encoding_dims=[8, 4, 2],
        dropout_rate=0.1
    )
    
    print(f"Created autoencoder with input_dim={input_dim}")
    print(f"Encoding dimensions: {autoencoder.encoding_dims}")
    
    # Create trainer
    trainer = AutoencoderTrainer(autoencoder)
    
    print("Training autoencoder...")
    result = trainer.train(
        X_train, 
        epochs=5, 
        batch_size=16,
        verbose=1
    )
    
    print(f"Training completed!")
    print(f"Reconstruction threshold: {result['reconstruction_threshold']:.4f}")
    print(f"Final loss: {result['training_metadata']['final_loss']:.4f}")
    
    # Test prediction
    print("\nTesting predictions...")
    predictor = AutoencoderPredictor(autoencoder)
    
    # Test risk score prediction
    risk_scores = predictor.predict_risk_score(X_test)
    print(f"Risk scores shape: {risk_scores.shape}")
    print(f"Risk scores range: [{risk_scores.min():.4f}, {risk_scores.max():.4f}]")
    print(f"Mean risk score: {risk_scores.mean():.4f}")
    
    # Test anomaly detection
    anomalies = predictor.predict_anomaly(X_test, threshold=0.5)
    anomaly_count = np.sum(anomalies)
    print(f"Detected {anomaly_count} anomalies out of {len(X_test)} samples")
    
    # Test reconstruction
    reconstructions = autoencoder.predict(X_test[:3])
    reconstruction_errors = autoencoder.get_reconstruction_errors(X_test[:3])
    print(f"Reconstruction errors for first 3 samples: {reconstruction_errors}")
    
    # Test latent representation
    latent = autoencoder.get_latent_representation(X_test[:3])
    print(f"Latent representation shape: {latent.shape}")
    
    # Test explanation
    feature_names = [f'feature_{i}' for i in range(input_dim)]
    explanation = predictor.explain_prediction(X_test[:2], feature_names)
    print(f"Explanation keys: {list(explanation.keys())}")
    
    print("\n✅ All basic tests passed!")
    return True


def test_data_validation():
    """Test data validation functionality"""
    print("\nTesting data validation...")
    
    input_dim = 5
    autoencoder = BehavioralAutoencoder(input_dim=input_dim)
    trainer = AutoencoderTrainer(autoencoder)
    
    # Test valid data
    X_valid = np.random.normal(0, 1, (20, input_dim))
    assert trainer.validate_data(X_valid), "Valid data should pass validation"
    print("✅ Valid data passed validation")
    
    # Test data preprocessing
    X_processed = trainer.preprocess_data(X_valid)
    assert X_processed.shape == X_valid.shape, "Preprocessing should preserve shape"
    assert np.all(X_processed >= 0) and np.all(X_processed <= 1), "Data should be normalized to [0,1]"
    print("✅ Data preprocessing works correctly")
    
    print("✅ Data validation tests passed!")
    return True


def test_model_persistence():
    """Test model saving and loading"""
    print("\nTesting model persistence...")
    
    input_dim = 6
    X_train = np.random.normal(0, 1, (30, input_dim))
    
    # Create and train model
    autoencoder = BehavioralAutoencoder(input_dim=input_dim)
    trainer = AutoencoderTrainer(autoencoder)
    trainer.train(X_train, epochs=2, verbose=0)
    
    # Test model info
    model_info = autoencoder.get_model_info()
    print(f"Model info: {model_info}")
    assert model_info['is_trained'], "Model should be marked as trained"
    assert model_info['feature_count'] == 0, "No feature names set"
    
    print("✅ Model persistence tests passed!")
    return True


if __name__ == "__main__":
    print("🔐 TrustChain-Auth Autoencoder Test Suite")
    print("=" * 50)
    
    try:
        # Run all tests
        test_basic_functionality()
        test_data_validation()
        test_model_persistence()
        
        print("\n🎉 All tests completed successfully!")
        print("The autoencoder implementation is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
