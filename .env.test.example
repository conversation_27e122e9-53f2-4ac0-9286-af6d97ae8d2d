# Test Environment Settings
ENV=test
DEBUG=true
PROJECT_NAME=TrustChain-Auth
API_V1_STR=/api/v1

# Test Database (using SQLite for tests)
SQLALCHEMY_DATABASE_URI=sqlite+aiosqlite:///./test.db

# JWT (use consistent key for tests)
JWT_SECRET_KEY=test-secret-key-for-testing-only
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Test Redis (mock in memory for tests)
REDIS_URL=memory://

# Monitoring (disabled for tests)
SENTRY_DSN=
PROMETHEUS_MULTIPROC_DIR=/tmp

# Security
CORS_ORIGINS=["http://test"]
TRUSTED_HOSTS=["test"]

# ML Service (mock for tests)
ML_SERVICE_URL=http://test

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=json
