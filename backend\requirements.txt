# FastAPI Backend Dependencies
fastapi==0.115.13
uvicorn[standard]==0.34.3
pydantic==2.11.7
pydantic-settings==2.10.0
pydantic[email]  # Needed for email validation

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9  # PostgreSQL
asyncpg==0.29.0  # Async PostgreSQL
aiosqlite==0.19.0  # For testing with SQLite

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2
cryptography==41.0.7
email-validator>=2.0.0  # Required by pydantic[email]

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Monitoring & Logging
prometheus-client==0.22.1
structlog==25.4.0
sentry-sdk[fastapi]==2.30.0
python-json-logger==3.3.0

# Configuration
python-dotenv==1.1.0
pyyaml==6.0.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
coverage==7.9.1
propcache==0.3.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.5.1
pre-commit==3.5.0

# Type stubs for better IDE support
types-python-jose
types-passlib
types-pyyaml
types-redis
types-requests
