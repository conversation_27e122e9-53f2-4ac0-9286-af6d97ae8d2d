"""
Behavioral Data Structures for TrustChain-Auth
Core data models for capturing behavioral biometrics
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
import numpy as np
import json
from enum import Enum


class EventType(Enum):
    """Types of behavioral events"""
    KEY_DOWN = "key_down"
    KEY_UP = "key_up"
    TOUCH_DOWN = "touch_down"
    TOUCH_MOVE = "touch_move"
    TOUCH_UP = "touch_up"
    SWIPE = "swipe"
    TAP = "tap"
    LONG_PRESS = "long_press"
    NAVIGATION = "navigation"
    SCREEN_CHANGE = "screen_change"


@dataclass
class TypingDynamics:
    """Captures typing behavior patterns"""
    key_code: str
    key_down_time: float
    key_up_time: float
    dwell_time: float  # Time key was held down
    flight_time: Optional[float] = None  # Time between key releases
    pressure: Optional[float] = None
    typing_speed: Optional[float] = None
    rhythm_pattern: Optional[List[float]] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def inter_key_interval(self) -> Optional[float]:
        """Time between consecutive keystrokes"""
        return self.flight_time
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'key_code': self.key_code,
            'key_down_time': self.key_down_time,
            'key_up_time': self.key_up_time,
            'dwell_time': self.dwell_time,
            'flight_time': self.flight_time,
            'pressure': self.pressure,
            'typing_speed': self.typing_speed,
            'rhythm_pattern': self.rhythm_pattern,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class TouchBehavior:
    """Captures touch interaction patterns"""
    event_type: EventType
    x_coordinate: float
    y_coordinate: float
    pressure: float
    touch_area: float
    velocity: Optional[Tuple[float, float]] = None  # (vx, vy)
    acceleration: Optional[Tuple[float, float]] = None
    gesture_type: Optional[str] = None
    duration: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def touch_intensity(self) -> float:
        """Calculate touch intensity based on pressure and area"""
        return self.pressure * self.touch_area
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'event_type': self.event_type.value,
            'x_coordinate': self.x_coordinate,
            'y_coordinate': self.y_coordinate,
            'pressure': self.pressure,
            'touch_area': self.touch_area,
            'velocity': self.velocity,
            'acceleration': self.acceleration,
            'gesture_type': self.gesture_type,
            'duration': self.duration,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class NavigationPattern:
    """Captures app navigation behavior"""
    from_screen: str
    to_screen: str
    navigation_time: float
    interaction_sequence: List[str]
    scroll_behavior: Optional[Dict[str, float]] = None
    tap_locations: Optional[List[Tuple[float, float]]] = None
    session_duration: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'from_screen': self.from_screen,
            'to_screen': self.to_screen,
            'navigation_time': self.navigation_time,
            'interaction_sequence': self.interaction_sequence,
            'scroll_behavior': self.scroll_behavior,
            'tap_locations': self.tap_locations,
            'session_duration': self.session_duration,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class ContextualData:
    """Captures contextual information"""
    device_orientation: str
    battery_level: Optional[float] = None
    network_type: Optional[str] = None
    location_context: Optional[str] = None  # home, work, public, etc.
    time_of_day: Optional[str] = None
    day_of_week: Optional[str] = None
    app_version: Optional[str] = None
    device_model: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'device_orientation': self.device_orientation,
            'battery_level': self.battery_level,
            'network_type': self.network_type,
            'location_context': self.location_context,
            'time_of_day': self.time_of_day,
            'day_of_week': self.day_of_week,
            'app_version': self.app_version,
            'device_model': self.device_model,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class BehavioralSession:
    """Represents a complete behavioral data collection session"""
    session_id: str
    user_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    typing_data: List[TypingDynamics] = field(default_factory=list)
    touch_data: List[TouchBehavior] = field(default_factory=list)
    navigation_data: List[NavigationPattern] = field(default_factory=list)
    contextual_data: List[ContextualData] = field(default_factory=list)
    session_metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def session_duration(self) -> Optional[float]:
        """Calculate session duration in seconds"""
        if self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def total_events(self) -> int:
        """Total number of behavioral events in session"""
        return (len(self.typing_data) + len(self.touch_data) + 
                len(self.navigation_data) + len(self.contextual_data))
    
    def add_typing_event(self, typing_event: TypingDynamics):
        """Add typing dynamics event to session"""
        self.typing_data.append(typing_event)
    
    def add_touch_event(self, touch_event: TouchBehavior):
        """Add touch behavior event to session"""
        self.touch_data.append(touch_event)
    
    def add_navigation_event(self, nav_event: NavigationPattern):
        """Add navigation pattern event to session"""
        self.navigation_data.append(nav_event)
    
    def add_contextual_event(self, context_event: ContextualData):
        """Add contextual data event to session"""
        self.contextual_data.append(context_event)
    
    def end_session(self):
        """Mark session as ended"""
        self.end_time = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary for serialization"""
        return {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'session_duration': self.session_duration,
            'total_events': self.total_events,
            'typing_data': [event.to_dict() for event in self.typing_data],
            'touch_data': [event.to_dict() for event in self.touch_data],
            'navigation_data': [event.to_dict() for event in self.navigation_data],
            'contextual_data': [event.to_dict() for event in self.contextual_data],
            'session_metadata': self.session_metadata
        }
    
    def save_to_json(self, filepath: str):
        """Save session data to JSON file"""
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)


class BehavioralDataCollector:
    """Main collector class for behavioral data"""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.current_session: Optional[BehavioralSession] = None
        self.sessions: List[BehavioralSession] = []
    
    def start_session(self, session_id: Optional[str] = None) -> str:
        """Start a new behavioral data collection session"""
        if session_id is None:
            session_id = f"{self.user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_session = BehavioralSession(
            session_id=session_id,
            user_id=self.user_id,
            start_time=datetime.now()
        )
        return session_id
    
    def end_session(self) -> Optional[BehavioralSession]:
        """End current session and return it"""
        if self.current_session:
            self.current_session.end_session()
            self.sessions.append(self.current_session)
            completed_session = self.current_session
            self.current_session = None
            return completed_session
        return None
    
    def collect_typing_event(self, typing_event: TypingDynamics):
        """Collect typing dynamics event"""
        if self.current_session:
            self.current_session.add_typing_event(typing_event)
    
    def collect_touch_event(self, touch_event: TouchBehavior):
        """Collect touch behavior event"""
        if self.current_session:
            self.current_session.add_touch_event(touch_event)
    
    def collect_navigation_event(self, nav_event: NavigationPattern):
        """Collect navigation pattern event"""
        if self.current_session:
            self.current_session.add_navigation_event(nav_event)
    
    def collect_contextual_event(self, context_event: ContextualData):
        """Collect contextual data event"""
        if self.current_session:
            self.current_session.add_contextual_event(context_event)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of current session"""
        if not self.current_session:
            return {}
        
        return {
            'session_id': self.current_session.session_id,
            'duration': self.current_session.session_duration,
            'total_events': self.current_session.total_events,
            'typing_events': len(self.current_session.typing_data),
            'touch_events': len(self.current_session.touch_data),
            'navigation_events': len(self.current_session.navigation_data),
            'contextual_events': len(self.current_session.contextual_data)
        }
