"""Configuration management for <PERSON><PERSON><PERSON><PERSON>-Auth backend."""

from pydantic_settings import BaseSettings
from typing import Optional, List, Union
import os
from functools import lru_cache

class Settings(BaseSettings):
    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Trust<PERSON><PERSON><PERSON>-Auth"
    VERSION: str = "1.0.0"
    ENV: str = "development"
    DEBUG: bool = True
    
    # Security
    SECRET_KEY: str = "dev-key-do-not-use-in-production-12345678901234567890"
    JWT_SECRET_KEY: str = "jwt-dev-key-do-not-use-in-production-12345678901234567890"
    JWT_ALGORITHM: str = "HS256"  # Used by JWT encoding/decoding
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    # Database
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "trustchain"
    SQLALCHEMY_DATABASE_URI: Optional[str] = None
    
    # Test Settings
    USE_TEST_DB: bool = False
    TEST_DB_URL: Optional[str] = "sqlite+aiosqlite:///./test.db"
    
    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    
    # ML Model Service
    MODEL_SERVER_URL: str = "http://localhost:8501"
    MODEL_VERSION: str = "v1"
    
    # Monitoring
    SENTRY_DSN: Optional[str] = None
    PROMETHEUS_ENABLED: bool = True
    
    # Firebase (Optional)
    FIREBASE_CREDENTIALS_PATH: Optional[str] = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        if self.ENV == "test" or self.USE_TEST_DB:
            self.SQLALCHEMY_DATABASE_URI = self.TEST_DB_URL
        else:
            self.SQLALCHEMY_DATABASE_URI = (
                f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}"
                f"@{self.POSTGRES_SERVER}/{self.POSTGRES_DB}"
            )

    model_config = {
        "env_file": ".env" if not (os.getenv("ENV") == "test" or os.path.exists(".env.test")) else ".env.test",
        "case_sensitive": True,
        "extra": "allow"
    }

@lru_cache()
def get_settings() -> Settings:
    return Settings()

# Instance of settings to be imported by other modules
settings = get_settings()
