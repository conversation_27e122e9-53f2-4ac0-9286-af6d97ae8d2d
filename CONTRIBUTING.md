# Contributing to <PERSON><PERSON><PERSON><PERSON>-Auth

## 🎯 Project Structure

The project is organized into several key components:

### 1. Backend (`/backend`)
- FastAPI server for authentication and behavioral analytics
- SQLAlchemy for async database operations
- Redis for caching and session management
- Prometheus/Grafana for monitoring

### 2. ML Models (`/ml_models`)
- Behavioral biometrics models using TensorFlow
- Feature extraction and preprocessing pipelines
- Model training and evaluation scripts
- TensorFlow Lite model conversion

### 3. Mobile App (`/mobile_app`)
- Flutter-based mobile application
- On-device ML inference with TFLite
- Real-time behavioral monitoring
- Privacy-focused data collection

## 🚀 Getting Started

### Environment Setup

1. Python Environment (3.11+ required):
```bash
# Create virtual environment
python -m venv .venv

# Activate virtual environment
# Windows
.venv\Scripts\activate
# Linux/macOS
source .venv/bin/activate

# Install development dependencies
pip install -e ".[test]"  # Installs package with test dependencies
```

2. Required System Dependencies:
- PostgreSQL 15+
- Redis 7+
- Node.js 18+ (for development tools)
- Android SDK (for mobile app development)
- Flutter SDK 3.0+

3. Environment Variables:
```bash
# Copy example environment files
cp .env.example .env
cp .env.test.example .env.test

# Update with your settings
# Required variables:
# - DATABASE_URL
# - REDIS_URL
# - JWT_SECRET_KEY
# - SENTRY_DSN (optional for development)
```

4. Initialize Database:
```bash
# Create databases
createdb trustchain_dev
createdb trustchain_test  # For running tests

# Run migrations
cd backend
alembic upgrade head
```

5. Start the services:
```bash
docker-compose up -d
```

### Running Tests

Before running tests, ensure:
1. Test database exists and is accessible
2. .env.test is properly configured
3. All test dependencies are installed

```bash
# Run backend tests
cd backend && pytest -v

# Run specific test file
pytest tests/test_api.py -v

# Run ML model tests
cd ml_models && pytest

# Run mobile app tests
cd mobile_app && flutter test
```

### Common Issues

1. Package Import Errors:
   - Ensure you're in the virtual environment
   - Check PYTHONPATH includes backend/ and ml_models/src/
   - Verify all dependencies are installed: `pip install -e ".[test]"`

2. Database Errors:
   - Verify PostgreSQL is running
   - Check database URLs in .env files
   - Ensure migrations are up to date

3. Test Failures:
   - Clean test database: `dropdb trustchain_test && createdb trustchain_test`
   - Run migrations on test database
   - Check .env.test configuration

## 📝 Code Style

- Backend: Black + isort for Python
- Mobile: Flutter format for Dart
- Documentation: Keep READMEs updated
- Tests: Required for all new features

## 🤝 Pull Request Process

1. Create feature branch from `develop`
2. Add tests and documentation
3. Ensure all CI checks pass
4. Request review from maintainers

## 📚 Documentation

- `README.md` - Project overview
- `docs/architecture/` - Technical design
- `DEPLOYMENT_GUIDE.md` - Deployment steps
- `MANUAL_TESTING_GUIDE.md` - Testing procedures

## 🔒 Security

- Report vulnerabilities privately
- Follow secure coding guidelines
- Keep dependencies updated
- Use provided security tools

## 📜 License

This project is licensed under the terms specified in `LICENSE`.
