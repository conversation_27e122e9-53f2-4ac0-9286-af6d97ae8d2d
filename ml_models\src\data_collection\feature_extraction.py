"""
Feature Extraction for Behavioral Biometrics
Converts raw behavioral data into ML-ready features
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
from scipy import stats
from scipy.signal import find_peaks
import warnings
warnings.filterwarnings('ignore')

from .behavioral_data import (
    TypingDynamics, TouchBehavior, NavigationPattern, 
    ContextualData, BehavioralSession
)


class TypingFeatureExtractor:
    """Extract features from typing dynamics data"""
    
    @staticmethod
    def extract_timing_features(typing_events: List[TypingDynamics]) -> Dict[str, float]:
        """Extract timing-based features from typing events"""
        if not typing_events:
            return {}
        
        dwell_times = [event.dwell_time for event in typing_events]
        flight_times = [event.flight_time for event in typing_events if event.flight_time is not None]
        
        features = {}
        
        # Dwell time statistics
        if dwell_times:
            features.update({
                'dwell_time_mean': np.mean(dwell_times),
                'dwell_time_std': np.std(dwell_times),
                'dwell_time_median': np.median(dwell_times),
                'dwell_time_min': np.min(dwell_times),
                'dwell_time_max': np.max(dwell_times),
                'dwell_time_range': np.max(dwell_times) - np.min(dwell_times),
                'dwell_time_skewness': stats.skew(dwell_times),
                'dwell_time_kurtosis': stats.kurtosis(dwell_times)
            })
        
        # Flight time statistics
        if flight_times:
            features.update({
                'flight_time_mean': np.mean(flight_times),
                'flight_time_std': np.std(flight_times),
                'flight_time_median': np.median(flight_times),
                'flight_time_min': np.min(flight_times),
                'flight_time_max': np.max(flight_times),
                'flight_time_range': np.max(flight_times) - np.min(flight_times),
                'flight_time_skewness': stats.skew(flight_times),
                'flight_time_kurtosis': stats.kurtosis(flight_times)
            })
        
        return features
    
    @staticmethod
    def extract_rhythm_features(typing_events: List[TypingDynamics]) -> Dict[str, float]:
        """Extract rhythm and pattern features"""
        if len(typing_events) < 3:
            return {}
        
        # Calculate inter-keystroke intervals
        intervals = []
        for i in range(1, len(typing_events)):
            if typing_events[i-1].key_up_time and typing_events[i].key_down_time:
                interval = typing_events[i].key_down_time - typing_events[i-1].key_up_time
                intervals.append(interval)
        
        if not intervals:
            return {}
        
        features = {
            'typing_rhythm_consistency': 1.0 / (1.0 + np.std(intervals)),
            'typing_speed_variance': np.var(intervals),
            'rhythm_entropy': stats.entropy(np.histogram(intervals, bins=10)[0] + 1e-10)
        }
        
        # Detect typing bursts (periods of rapid typing)
        if len(intervals) > 5:
            peaks, _ = find_peaks(-np.array(intervals), height=-np.mean(intervals))
            features['typing_burst_frequency'] = len(peaks) / len(intervals)
        
        return features
    
    @staticmethod
    def extract_pressure_features(typing_events: List[TypingDynamics]) -> Dict[str, float]:
        """Extract pressure-based features if available"""
        pressures = [event.pressure for event in typing_events if event.pressure is not None]
        
        if not pressures:
            return {}
        
        return {
            'pressure_mean': np.mean(pressures),
            'pressure_std': np.std(pressures),
            'pressure_range': np.max(pressures) - np.min(pressures),
            'pressure_consistency': 1.0 / (1.0 + np.std(pressures))
        }


class TouchFeatureExtractor:
    """Extract features from touch behavior data"""
    
    @staticmethod
    def extract_touch_dynamics(touch_events: List[TouchBehavior]) -> Dict[str, float]:
        """Extract touch dynamics features"""
        if not touch_events:
            return {}
        
        pressures = [event.pressure for event in touch_events]
        touch_areas = [event.touch_area for event in touch_events]
        
        features = {}
        
        # Pressure features
        if pressures:
            features.update({
                'touch_pressure_mean': np.mean(pressures),
                'touch_pressure_std': np.std(pressures),
                'touch_pressure_max': np.max(pressures),
                'touch_pressure_range': np.max(pressures) - np.min(pressures)
            })
        
        # Touch area features
        if touch_areas:
            features.update({
                'touch_area_mean': np.mean(touch_areas),
                'touch_area_std': np.std(touch_areas),
                'touch_area_consistency': 1.0 / (1.0 + np.std(touch_areas))
            })
        
        return features
    
    @staticmethod
    def extract_gesture_features(touch_events: List[TouchBehavior]) -> Dict[str, float]:
        """Extract gesture-based features"""
        if len(touch_events) < 2:
            return {}
        
        # Calculate velocities and accelerations
        velocities = []
        accelerations = []
        
        for event in touch_events:
            if event.velocity:
                vel_magnitude = np.sqrt(event.velocity[0]**2 + event.velocity[1]**2)
                velocities.append(vel_magnitude)
            
            if event.acceleration:
                acc_magnitude = np.sqrt(event.acceleration[0]**2 + event.acceleration[1]**2)
                accelerations.append(acc_magnitude)
        
        features = {}
        
        if velocities:
            features.update({
                'gesture_velocity_mean': np.mean(velocities),
                'gesture_velocity_std': np.std(velocities),
                'gesture_velocity_max': np.max(velocities)
            })
        
        if accelerations:
            features.update({
                'gesture_acceleration_mean': np.mean(accelerations),
                'gesture_acceleration_std': np.std(accelerations),
                'gesture_smoothness': 1.0 / (1.0 + np.std(accelerations))
            })
        
        return features
    
    @staticmethod
    def extract_spatial_features(touch_events: List[TouchBehavior]) -> Dict[str, float]:
        """Extract spatial distribution features"""
        if not touch_events:
            return {}
        
        x_coords = [event.x_coordinate for event in touch_events]
        y_coords = [event.y_coordinate for event in touch_events]
        
        features = {
            'touch_x_spread': np.std(x_coords),
            'touch_y_spread': np.std(y_coords),
            'touch_area_coverage': (np.max(x_coords) - np.min(x_coords)) * 
                                 (np.max(y_coords) - np.min(y_coords)),
            'touch_center_x': np.mean(x_coords),
            'touch_center_y': np.mean(y_coords)
        }
        
        return features


class NavigationFeatureExtractor:
    """Extract features from navigation patterns"""
    
    @staticmethod
    def extract_navigation_timing(nav_events: List[NavigationPattern]) -> Dict[str, float]:
        """Extract navigation timing features"""
        if not nav_events:
            return {}
        
        nav_times = [event.navigation_time for event in nav_events]
        session_durations = [event.session_duration for event in nav_events 
                           if event.session_duration is not None]
        
        features = {}
        
        if nav_times:
            features.update({
                'navigation_time_mean': np.mean(nav_times),
                'navigation_time_std': np.std(nav_times),
                'navigation_efficiency': 1.0 / np.mean(nav_times)
            })
        
        if session_durations:
            features.update({
                'session_duration_mean': np.mean(session_durations),
                'session_duration_consistency': 1.0 / (1.0 + np.std(session_durations))
            })
        
        return features
    
    @staticmethod
    def extract_interaction_patterns(nav_events: List[NavigationPattern]) -> Dict[str, float]:
        """Extract interaction pattern features"""
        if not nav_events:
            return {}
        
        # Analyze screen transition patterns
        transitions = [(event.from_screen, event.to_screen) for event in nav_events]
        unique_transitions = set(transitions)
        
        # Calculate interaction sequence complexity
        all_sequences = []
        for event in nav_events:
            all_sequences.extend(event.interaction_sequence)
        
        features = {
            'unique_transitions': len(unique_transitions),
            'transition_diversity': len(unique_transitions) / len(transitions) if transitions else 0,
            'interaction_complexity': len(set(all_sequences)) / len(all_sequences) if all_sequences else 0
        }
        
        return features


class ContextualFeatureExtractor:
    """Extract features from contextual data"""
    
    @staticmethod
    def extract_temporal_features(context_events: List[ContextualData]) -> Dict[str, float]:
        """Extract temporal context features"""
        if not context_events:
            return {}
        
        # Analyze time-of-day patterns
        time_patterns = {}
        for event in context_events:
            if event.time_of_day:
                time_patterns[event.time_of_day] = time_patterns.get(event.time_of_day, 0) + 1
        
        # Analyze day-of-week patterns
        day_patterns = {}
        for event in context_events:
            if event.day_of_week:
                day_patterns[event.day_of_week] = day_patterns.get(event.day_of_week, 0) + 1
        
        features = {
            'temporal_consistency': len(time_patterns) / len(context_events) if context_events else 0,
            'weekly_pattern_diversity': len(day_patterns)
        }
        
        return features
    
    @staticmethod
    def extract_environmental_features(context_events: List[ContextualData]) -> Dict[str, float]:
        """Extract environmental context features"""
        if not context_events:
            return {}
        
        battery_levels = [event.battery_level for event in context_events 
                         if event.battery_level is not None]
        
        features = {}
        
        if battery_levels:
            features.update({
                'battery_level_mean': np.mean(battery_levels),
                'battery_level_variance': np.var(battery_levels)
            })
        
        # Analyze location context patterns
        location_contexts = [event.location_context for event in context_events 
                           if event.location_context is not None]
        if location_contexts:
            unique_locations = set(location_contexts)
            features['location_diversity'] = len(unique_locations)
        
        return features


class BehavioralFeatureExtractor:
    """Main feature extractor that combines all behavioral data"""
    
    def __init__(self):
        self.typing_extractor = TypingFeatureExtractor()
        self.touch_extractor = TouchFeatureExtractor()
        self.navigation_extractor = NavigationFeatureExtractor()
        self.contextual_extractor = ContextualFeatureExtractor()
    
    def extract_session_features(self, session: BehavioralSession) -> Dict[str, float]:
        """Extract comprehensive features from a behavioral session"""
        features = {}
        
        # Extract typing features
        typing_features = {}
        typing_features.update(self.typing_extractor.extract_timing_features(session.typing_data))
        typing_features.update(self.typing_extractor.extract_rhythm_features(session.typing_data))
        typing_features.update(self.typing_extractor.extract_pressure_features(session.typing_data))
        
        # Extract touch features
        touch_features = {}
        touch_features.update(self.touch_extractor.extract_touch_dynamics(session.touch_data))
        touch_features.update(self.touch_extractor.extract_gesture_features(session.touch_data))
        touch_features.update(self.touch_extractor.extract_spatial_features(session.touch_data))
        
        # Extract navigation features
        nav_features = {}
        nav_features.update(self.navigation_extractor.extract_navigation_timing(session.navigation_data))
        nav_features.update(self.navigation_extractor.extract_interaction_patterns(session.navigation_data))
        
        # Extract contextual features
        context_features = {}
        context_features.update(self.contextual_extractor.extract_temporal_features(session.contextual_data))
        context_features.update(self.contextual_extractor.extract_environmental_features(session.contextual_data))
        
        # Combine all features
        features.update({f'typing_{k}': v for k, v in typing_features.items()})
        features.update({f'touch_{k}': v for k, v in touch_features.items()})
        features.update({f'navigation_{k}': v for k, v in nav_features.items()})
        features.update({f'contextual_{k}': v for k, v in context_features.items()})
        
        # Add session-level features
        features.update({
            'session_duration': session.session_duration or 0,
            'total_events': session.total_events,
            'typing_event_ratio': len(session.typing_data) / session.total_events if session.total_events > 0 else 0,
            'touch_event_ratio': len(session.touch_data) / session.total_events if session.total_events > 0 else 0,
            'navigation_event_ratio': len(session.navigation_data) / session.total_events if session.total_events > 0 else 0
        })
        
        return features
    
    def extract_features_batch(self, sessions: List[BehavioralSession]) -> pd.DataFrame:
        """Extract features from multiple sessions and return as DataFrame"""
        feature_list = []
        
        for session in sessions:
            features = self.extract_session_features(session)
            features['session_id'] = session.session_id
            features['user_id'] = session.user_id
            feature_list.append(features)
        
        return pd.DataFrame(feature_list)
    
    def get_feature_names(self) -> List[str]:
        """Get list of all possible feature names"""
        # This would return all possible feature names
        # For now, return a basic set
        return [
            'typing_dwell_time_mean', 'typing_dwell_time_std', 'typing_flight_time_mean',
            'touch_pressure_mean', 'touch_area_mean', 'gesture_velocity_mean',
            'navigation_time_mean', 'session_duration', 'total_events'
        ]
