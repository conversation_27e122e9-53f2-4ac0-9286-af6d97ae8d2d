"""
ML Models for TrustChain-Auth Behavioral Biometrics
"""

from .base_model import (
    BaseBehavioralModel,
    ModelTrainer,
    ModelPredictor
)

from .autoencoder import (
    BehavioralAutoencoder,
    AutoencoderTrainer,
    AutoencoderPredictor
)

from .one_class_svm import (
    BehavioralOneClassSVM,
    OneClassSVMTrainer,
    OneClassSVMPredictor
)

from .contrastive_learning import (
    ContrastiveLearningModel,
    ContrastiveTrainer,
    ContrastivePredictor
)

# from .ensemble import (
#     BehavioralEnsemble,
#     EnsemblePredictor
# )

__all__ = [
    'BaseBehavioralModel',
    'ModelTrainer',
    'ModelPredictor',
    'BehavioralAutoencoder',
    'AutoencoderTrainer',
    'AutoencoderPredictor',
    'BehavioralOneClassSVM',
    'OneClassSVMTrainer',
    'OneClassSVMPredictor',
    'ContrastiveLearningModel',
    'ContrastiveTrainer',
    'ContrastivePredictor'
]
