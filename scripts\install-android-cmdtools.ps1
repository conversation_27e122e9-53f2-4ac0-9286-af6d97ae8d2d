# Install Android Command Line Tools
# This script downloads and installs the Android SDK command-line tools

param(
    [string]$AndroidSdkPath = "$env:LOCALAPPDATA\Android\sdk"
)

Write-Host "🔧 Installing Android SDK Command Line Tools..." -ForegroundColor Blue

# Check if Android SDK directory exists
if (-not (Test-Path $AndroidSdkPath)) {
    Write-Host "❌ Android SDK not found at: $AndroidSdkPath" -ForegroundColor Red
    Write-Host "Please install Android Studio first." -ForegroundColor Yellow
    exit 1
}

# Create cmdline-tools directory
$CmdlineToolsPath = Join-Path $AndroidSdkPath "cmdline-tools"
$LatestPath = Join-Path $CmdlineToolsPath "latest"

if (-not (Test-Path $CmdlineToolsPath)) {
    New-Item -ItemType Directory -Path $CmdlineToolsPath -Force | Out-Null
    Write-Host "✓ Created cmdline-tools directory" -ForegroundColor Green
}

if (Test-Path $LatestPath) {
    Write-Host "✓ Command line tools already installed" -ForegroundColor Green
    exit 0
}

# Download URL for Windows command line tools
$DownloadUrl = "https://dl.google.com/android/repository/commandlinetools-win-11076708_latest.zip"
$TempZipPath = Join-Path $env:TEMP "commandlinetools-win.zip"

try {
    Write-Host "📥 Downloading Android command line tools..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $DownloadUrl -OutFile $TempZipPath -UseBasicParsing
    Write-Host "✓ Download completed" -ForegroundColor Green

    Write-Host "📦 Extracting command line tools..." -ForegroundColor Yellow
    
    # Extract to temporary location first
    $TempExtractPath = Join-Path $env:TEMP "cmdline-tools-temp"
    if (Test-Path $TempExtractPath) {
        Remove-Item $TempExtractPath -Recurse -Force
    }
    
    Expand-Archive -Path $TempZipPath -DestinationPath $TempExtractPath -Force
    
    # Move the extracted cmdline-tools to the correct location
    $ExtractedPath = Join-Path $TempExtractPath "cmdline-tools"
    if (Test-Path $ExtractedPath) {
        Move-Item $ExtractedPath $LatestPath -Force
        Write-Host "✓ Command line tools installed successfully" -ForegroundColor Green
    } else {
        throw "Extracted files not found in expected location"
    }

    # Clean up
    Remove-Item $TempZipPath -Force -ErrorAction SilentlyContinue
    Remove-Item $TempExtractPath -Recurse -Force -ErrorAction SilentlyContinue

    Write-Host "🎉 Android SDK command line tools installation completed!" -ForegroundColor Green
    Write-Host "📍 Installed at: $LatestPath" -ForegroundColor Cyan

} catch {
    Write-Host "❌ Failed to install command line tools: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify installation
$SdkManagerPath = Join-Path $LatestPath "bin\sdkmanager.bat"
if (Test-Path $SdkManagerPath) {
    Write-Host "✓ SDK Manager found at: $SdkManagerPath" -ForegroundColor Green
    
    # Set ANDROID_HOME environment variable if not set
    if (-not $env:ANDROID_HOME) {
        Write-Host "🔧 Setting ANDROID_HOME environment variable..." -ForegroundColor Yellow
        [Environment]::SetEnvironmentVariable("ANDROID_HOME", $AndroidSdkPath, "User")
        $env:ANDROID_HOME = $AndroidSdkPath
        Write-Host "✓ ANDROID_HOME set to: $AndroidSdkPath" -ForegroundColor Green
    }
    
    Write-Host "`n📋 Next steps:" -ForegroundColor Cyan
    Write-Host "1. Restart your terminal/IDE to pick up environment changes" -ForegroundColor White
    Write-Host "2. Run 'flutter doctor --android-licenses' to accept licenses" -ForegroundColor White
    Write-Host "3. Run 'flutter doctor' to verify setup" -ForegroundColor White
    
} else {
    Write-Host "❌ SDK Manager not found after installation" -ForegroundColor Red
    exit 1
}
