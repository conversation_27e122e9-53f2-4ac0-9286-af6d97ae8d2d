"""
Trust<PERSON>hain-Auth Backend API
FastAPI backend for behavioral biometrics authentication system
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional, List
import uvicorn
from datetime import datetime
import uuid
import structlog

from core.config import settings
from core.logging_config import setup_logging, logger
from core.deps import get_current_user
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from prometheus_client import make_asgi_app

# API Routers
from api.v1 import router as api_v1_router

# In-memory storage (replace with database in production)
auth_events = []
security_alerts = []

# Initialize Sentry if DSN is configured
# This is commented out for development since SENTRY_DSN is not set
# if settings.SENTRY_DSN:
#     sentry_sdk.init(
#         dsn=settings.SENTRY_DSN,
#         environment=settings.ENV,
#         integrations=[FastApiIntegration()]
#     )

# Setup logging
setup_logging()

# Initialize FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="Backend API for behavioral biometrics authentication system",
    version=settings.VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# Add Prometheus metrics endpoint
if settings.PROMETHEUS_ENABLED:
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Trusted Host Middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else [
        "trustchain-auth.com",
        "api.trustchain-auth.com",
        "localhost",
        "localhost:8000"
    ]
)

# Custom middleware for request ID and logging
@app.middleware("http")
async def add_request_id_and_log(request: Request, call_next):
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    structlog.contextvars.clear_contextvars()
    structlog.contextvars.bind_contextvars(request_id=request_id)
    
    logger.info(
        "request_started",
        path=request.url.path,
        method=request.method
    )
    try:
        response = await call_next(request)
        logger.info(
            "request_completed",
            path=request.url.path,
            status_code=response.status_code
        )
        response.headers["X-Request-ID"] = request_id
        return response
    except Exception as e:
        logger.exception(
            "request_failed",
            path=request.url.path,
            error=str(e)
        )
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error", "request_id": request_id}
        )

# Security
security = HTTPBearer()

# Pydantic models
class AuthEvent(BaseModel):
    user_id: str
    event_type: str  # login_attempt, risk_assessment, challenge_triggered, etc.
    risk_score: float
    timestamp: datetime
    device_info: Optional[dict] = None
    location: Optional[dict] = None
    success: bool

class SecurityAlert(BaseModel):
    user_id: str
    alert_type: str  # high_risk, panic_gesture, suspicious_activity
    severity: str  # low, medium, high, critical
    description: str
    timestamp: datetime
    device_info: Optional[dict] = None

class HealthCheck(BaseModel):
    status: str
    timestamp: datetime
    version: str

# Include routers
app.include_router(api_v1_router)

@app.post("/api/v1/security/alerts")
async def create_security_alert(
    alert: SecurityAlert,
    current_user: dict = Depends(get_current_user)
):
    """Create security alerts for high-risk events"""
    try:
        security_alerts.append(alert)
        logger.warning(f"Security alert: {alert.alert_type} - {alert.description}")
        
        # In production, trigger notifications here
        # - Send email/SMS alerts
        # - Push notifications
        # - Integration with security systems
        
        return {"status": "success", "message": "Alert created successfully"}
    except Exception as e:
        logger.error(f"Error creating security alert: {e}")
        raise HTTPException(status_code=500, detail="Failed to create alert")

@app.get("/api/v1/auth/events/{user_id}")
async def get_user_auth_events(
    user_id: str,
    limit: int = 100,
    current_user: dict = Depends(get_current_user)
):
    """Get authentication events for a specific user"""
    try:
        user_events = [event for event in auth_events if event.user_id == user_id]
        return {"events": user_events[-limit:]}  # Return latest events
    except Exception as e:
        logger.error(f"Error retrieving auth events: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve events")

@app.get("/api/v1/security/alerts/{user_id}")
async def get_user_security_alerts(
    user_id: str,
    limit: int = 50,
    current_user: dict = Depends(get_current_user)
):
    """Get security alerts for a specific user"""
    try:
        user_alerts = [alert for alert in security_alerts if alert.user_id == user_id]
        return {"alerts": user_alerts[-limit:]}  # Return latest alerts
    except Exception as e:
        logger.error(f"Error retrieving security alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve alerts")

@app.get("/api/v1/analytics/risk-trends")
async def get_risk_trends(
    current_user: dict = Depends(get_current_user)
):
    """Get risk score trends for analytics"""
    try:
        # Calculate basic analytics from auth events
        if not auth_events:
            return {"trends": [], "average_risk": 0.0}
        
        risk_scores = [event.risk_score for event in auth_events]
        average_risk = sum(risk_scores) / len(risk_scores)
        
        return {
            "trends": risk_scores[-100:],  # Last 100 risk scores
            "average_risk": average_risk,
            "total_events": len(auth_events)
        }
    except Exception as e:
        logger.error(f"Error calculating risk trends: {e}")
        raise HTTPException(status_code=500, detail="Failed to calculate trends")

@app.get("/health")
async def health_check():
    """Detailed health check for monitoring"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "version": "1.0.0",
        "components": {
            "api": "healthy",
            "database": "healthy",  # Check database connection in production
            "cache": "healthy"      # Check Redis connection in production
        }
    }

if __name__ == "__main__":
    # Development server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
