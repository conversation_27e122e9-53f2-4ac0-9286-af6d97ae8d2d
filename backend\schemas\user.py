"""User schemas."""

from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
from db.models import UserRole

class UserBase(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    full_name: Optional[str] = None
    role: Optional[UserRole] = UserRole.USER
    is_active: Optional[bool] = True

class UserCreate(UserBase):
    password: str
    device_info: Optional[dict] = None

class UserUpdate(UserBase):
    password: Optional[str] = None

class UserInDBBase(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class User(UserInDBBase):
    pass

class UserInDB(UserInDBBase):
    hashed_password: str
