"""
Base classes for behavioral biometrics models
"""

from abc import ABC, abstractmethod
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional, Union
import joblib
import json
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class BaseBehavioralModel(ABC):
    """Base class for all behavioral biometrics models"""
    
    def __init__(self, model_name: str, model_version: str = "1.0"):
        self.model_name = model_name
        self.model_version = model_version
        self.is_trained = False
        self.feature_names: Optional[List[str]] = None
        self.training_metadata: Dict[str, Any] = {}
        self.model_params: Dict[str, Any] = {}
        
    @abstractmethod
    def fit(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> 'BaseBehavioralModel':
        """Train the model on behavioral data"""
        pass
    
    @abstractmethod
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions on behavioral data"""
        pass
    
    @abstractmethod
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Predict probabilities/scores for behavioral data"""
        pass
    
    def save_model(self, filepath: str):
        """Save the trained model to disk"""
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")
        
        model_data = {
            'model_name': self.model_name,
            'model_version': self.model_version,
            'feature_names': self.feature_names,
            'training_metadata': self.training_metadata,
            'model_params': self.model_params,
            'is_trained': self.is_trained
        }
        
        # Save model-specific data
        model_data.update(self._get_model_data())
        
        joblib.dump(model_data, filepath)
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load a trained model from disk"""
        model_data = joblib.load(filepath)
        
        self.model_name = model_data['model_name']
        self.model_version = model_data['model_version']
        self.feature_names = model_data['feature_names']
        self.training_metadata = model_data['training_metadata']
        self.model_params = model_data['model_params']
        self.is_trained = model_data['is_trained']
        
        # Load model-specific data
        self._set_model_data(model_data)
        
        logger.info(f"Model loaded from {filepath}")
    
    @abstractmethod
    def _get_model_data(self) -> Dict[str, Any]:
        """Get model-specific data for saving"""
        pass
    
    @abstractmethod
    def _set_model_data(self, model_data: Dict[str, Any]):
        """Set model-specific data when loading"""
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            'model_name': self.model_name,
            'model_version': self.model_version,
            'is_trained': self.is_trained,
            'feature_count': len(self.feature_names) if self.feature_names else 0,
            'training_metadata': self.training_metadata
        }


class ModelTrainer(ABC):
    """Base class for model trainers"""
    
    def __init__(self, model: BaseBehavioralModel):
        self.model = model
        self.training_history: List[Dict[str, Any]] = []
    
    @abstractmethod
    def train(self, 
              X_train: np.ndarray, 
              y_train: Optional[np.ndarray] = None,
              X_val: Optional[np.ndarray] = None,
              y_val: Optional[np.ndarray] = None,
              **kwargs) -> Dict[str, Any]:
        """Train the model"""
        pass
    
    def validate_data(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> bool:
        """Validate training data"""
        if X is None or len(X) == 0:
            raise ValueError("Training data cannot be empty")
        
        if np.isnan(X).any():
            raise ValueError("Training data contains NaN values")
        
        if np.isinf(X).any():
            raise ValueError("Training data contains infinite values")
        
        if y is not None:
            if len(X) != len(y):
                raise ValueError("X and y must have the same number of samples")
        
        return True
    
    def preprocess_data(self, X: np.ndarray) -> np.ndarray:
        """Preprocess data before training"""
        # Basic preprocessing - can be overridden
        return X
    
    def log_training_step(self, step_info: Dict[str, Any]):
        """Log training step information"""
        step_info['timestamp'] = datetime.now().isoformat()
        self.training_history.append(step_info)


class ModelPredictor:
    """Base class for model predictors"""
    
    def __init__(self, model: BaseBehavioralModel):
        self.model = model
        if not model.is_trained:
            raise ValueError("Model must be trained before creating predictor")
    
    def predict_risk_score(self, X: np.ndarray) -> np.ndarray:
        """Predict risk scores (0-1 scale, higher = more risky)"""
        if not self.model.is_trained:
            raise ValueError("Model is not trained")
        
        # Get prediction probabilities
        proba = self.model.predict_proba(X)
        
        # Convert to risk scores (implementation depends on model type)
        return self._convert_to_risk_scores(proba)
    
    def predict_anomaly(self, X: np.ndarray, threshold: float = 0.5) -> np.ndarray:
        """Predict anomalies based on threshold"""
        risk_scores = self.predict_risk_score(X)
        return (risk_scores > threshold).astype(int)
    
    def predict_batch(self, X: np.ndarray, batch_size: int = 32) -> np.ndarray:
        """Predict in batches for large datasets"""
        n_samples = len(X)
        predictions = []
        
        for i in range(0, n_samples, batch_size):
            batch_end = min(i + batch_size, n_samples)
            batch_X = X[i:batch_end]
            batch_pred = self.predict_risk_score(batch_X)
            predictions.append(batch_pred)
        
        return np.concatenate(predictions)
    
    @abstractmethod
    def _convert_to_risk_scores(self, proba: np.ndarray) -> np.ndarray:
        """Convert model probabilities to risk scores"""
        pass
    
    def explain_prediction(self, X: np.ndarray, feature_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Provide explanation for predictions"""
        # Basic implementation - can be overridden for more sophisticated explanations
        risk_scores = self.predict_risk_score(X)
        
        explanation = {
            'risk_scores': risk_scores.tolist(),
            'mean_risk': float(np.mean(risk_scores)),
            'max_risk': float(np.max(risk_scores)),
            'min_risk': float(np.min(risk_scores))
        }
        
        if feature_names:
            explanation['feature_names'] = feature_names
        
        return explanation


class ModelEvaluator:
    """Utility class for model evaluation"""
    
    @staticmethod
    def evaluate_anomaly_detection(y_true: np.ndarray, 
                                 y_scores: np.ndarray,
                                 threshold: float = 0.5) -> Dict[str, float]:
        """Evaluate anomaly detection performance"""
        from sklearn.metrics import (
            roc_auc_score, precision_recall_curve, auc,
            precision_score, recall_score, f1_score
        )
        
        y_pred = (y_scores > threshold).astype(int)
        
        metrics = {
            'auc_roc': roc_auc_score(y_true, y_scores),
            'precision': precision_score(y_true, y_pred),
            'recall': recall_score(y_true, y_pred),
            'f1_score': f1_score(y_true, y_pred)
        }
        
        # Calculate AUC-PR
        precision, recall, _ = precision_recall_curve(y_true, y_scores)
        metrics['auc_pr'] = auc(recall, precision)
        
        return metrics
    
    @staticmethod
    def evaluate_user_verification(y_true: np.ndarray, 
                                 y_scores: np.ndarray,
                                 threshold: float = 0.5) -> Dict[str, float]:
        """Evaluate user verification performance"""
        y_pred = (y_scores > threshold).astype(int)
        
        # Calculate False Acceptance Rate (FAR) and False Rejection Rate (FRR)
        true_positives = np.sum((y_true == 1) & (y_pred == 1))
        false_positives = np.sum((y_true == 0) & (y_pred == 1))
        true_negatives = np.sum((y_true == 0) & (y_pred == 0))
        false_negatives = np.sum((y_true == 1) & (y_pred == 0))
        
        far = false_positives / (false_positives + true_negatives) if (false_positives + true_negatives) > 0 else 0
        frr = false_negatives / (false_negatives + true_positives) if (false_negatives + true_positives) > 0 else 0
        
        return {
            'far': far,  # False Acceptance Rate
            'frr': frr,  # False Rejection Rate
            'accuracy': (true_positives + true_negatives) / len(y_true),
            'equal_error_rate': (far + frr) / 2
        }
    
    @staticmethod
    def find_optimal_threshold(y_true: np.ndarray, 
                             y_scores: np.ndarray,
                             metric: str = 'f1') -> Tuple[float, float]:
        """Find optimal threshold for given metric"""
        from sklearn.metrics import precision_recall_curve, f1_score
        
        if metric == 'f1':
            thresholds = np.linspace(0, 1, 100)
            best_threshold = 0.5
            best_score = 0
            
            for threshold in thresholds:
                y_pred = (y_scores > threshold).astype(int)
                score = f1_score(y_true, y_pred)
                if score > best_score:
                    best_score = score
                    best_threshold = threshold
            
            return best_threshold, best_score
        
        elif metric == 'precision_recall':
            precision, recall, thresholds = precision_recall_curve(y_true, y_scores)
            f1_scores = 2 * (precision * recall) / (precision + recall + 1e-10)
            best_idx = np.argmax(f1_scores)
            return thresholds[best_idx], f1_scores[best_idx]
        
        else:
            raise ValueError(f"Unsupported metric: {metric}")


class ModelConfig:
    """Configuration class for models"""
    
    def __init__(self, config_dict: Optional[Dict[str, Any]] = None):
        self.config = config_dict or {}
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """Set configuration value"""
        self.config[key] = value
    
    def update(self, config_dict: Dict[str, Any]):
        """Update configuration"""
        self.config.update(config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return self.config.copy()
    
    def save(self, filepath: str):
        """Save configuration to JSON file"""
        with open(filepath, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def load(self, filepath: str):
        """Load configuration from JSON file"""
        with open(filepath, 'r') as f:
            self.config = json.load(f)
