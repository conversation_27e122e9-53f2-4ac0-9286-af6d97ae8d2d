#!/usr/bin/env python3
"""Train the behavioral biometrics model on collected data."""

import os
import sys
import argparse
import logging
import json
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime

# Add src to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.autoencoder import BehavioralAutoencoder
from src.data_collection.preprocessor import BehavioralFeatureExtractor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_training_data(data_dir: str) -> pd.DataFrame:
    """Load and combine training data from files.
    
    Args:
        data_dir: Directory containing training data files
        
    Returns:
        DataFrame with combined features
    """
    features_list = []
    
    # Look for all feature files
    feature_files = list(Path(data_dir).glob('*.parquet'))
    if not feature_files:
        raise ValueError(f"No training data found in {data_dir}")
        
    logger.info(f"Found {len(feature_files)} feature files")
    
    # Load and combine
    preprocessor = BehavioralFeatureExtractor()
    
    for file_path in feature_files:
        try:
            # Load raw events
            events_df = pd.read_parquet(file_path)
            
            # Extract typing features if present
            if 'typing_events' in events_df.columns:
                typing_data = events_df['typing_events'].tolist()
                typing_features = preprocessor.extract_typing_features(typing_data)
                features_list.append(typing_features)
            
            # Extract touch features if present
            if 'touch_events' in events_df.columns:
                touch_data = events_df['touch_events'].tolist()
                touch_features = preprocessor.extract_touch_features(touch_data)
                features_list.append(touch_features)
                
        except Exception as e:
            logger.warning(f"Error processing {file_path}: {e}")
    
    if not features_list:
        raise ValueError("No valid features extracted from data")
        
    # Combine all features
    features_df = pd.concat(features_list, axis=1)
    
    logger.info(f"Loaded {len(features_df)} samples with {len(features_df.columns)} features")
    return features_df

def train_model(args):
    """Train and save the model.
    
    Args:
        args: Command line arguments
    """
    # Load and preprocess data
    logger.info(f"Loading data from {args.data_dir}")
    features_df = load_training_data(args.data_dir)
    
    # Create preprocessor and transform features
    preprocessor = BehavioralFeatureExtractor()
    X = preprocessor.fit_transform(features_df)
    
    # Split into train/validation
    n_val = int(len(X) * args.val_split)
    indices = np.random.permutation(len(X))
    val_idx, train_idx = indices[:n_val], indices[n_val:]
    
    X_train = X[train_idx]
    X_val = X[val_idx]
    
    # Create and train model
    logger.info("Training autoencoder model")
    model = BehavioralAutoencoder(
        input_dim=X.shape[1],
        encoding_dims=args.encoding_dims,
        dropout_rate=args.dropout_rate,
        l2_reg=args.l2_reg
    )
    
    history = model.fit(
        X_train,
        validation_data=(X_val, X_val),
        epochs=args.epochs,
        batch_size=args.batch_size
    )
    
    # Compute threshold
    threshold = model.compute_reconstruction_threshold(
        X_train, 
        percentile=args.threshold_percentile
    )
    logger.info(f"Computed reconstruction threshold: {threshold:.4f}")
    
    # Save model and preprocessor
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    model_dir = os.path.join(args.output_dir, f'model_{timestamp}')
    os.makedirs(model_dir, exist_ok=True)
    
    logger.info(f"Saving model to {model_dir}")
    model.save(model_dir)
    
    logger.info(f"Saving preprocessor to {os.path.join(model_dir, 'preprocessor')}")
    preprocessor.save_preprocessor(os.path.join(model_dir, 'preprocessor'))
    
    # Save training metrics
    metrics = {
        'timestamp': timestamp,
        'n_samples': len(X),
        'n_features': X.shape[1],
        'train_loss': float(history['loss'][-1]),
        'val_loss': float(history['val_loss'][-1]) if 'val_loss' in history else None,
        'reconstruction_threshold': float(threshold),
        'model_params': model.model_params
    }
    
    with open(os.path.join(model_dir, 'metrics.json'), 'w') as f:
        json.dump(metrics, f, indent=2)
    
    logger.info("Training completed successfully!")

def main():
    parser = argparse.ArgumentParser(description="Train behavioral biometrics model")
    
    parser.add_argument(
        '--data_dir',
        type=str,
        required=True,
        help='Directory containing training data'
    )
    parser.add_argument(
        '--output_dir',
        type=str,
        default='models',
        help='Directory to save trained model'
    )
    parser.add_argument(
        '--encoding_dims',
        type=int,
        nargs='+',
        default=[96, 64, 32],
        help='Dimensions of encoding layers'
    )
    parser.add_argument(
        '--epochs',
        type=int,
        default=100,
        help='Number of training epochs'
    )
    parser.add_argument(
        '--batch_size',
        type=int,
        default=32,
        help='Training batch size'
    )
    parser.add_argument(
        '--dropout_rate',
        type=float,
        default=0.2,
        help='Dropout rate'
    )
    parser.add_argument(
        '--l2_reg',
        type=float,
        default=1e-4,
        help='L2 regularization factor'
    )
    parser.add_argument(
        '--val_split',
        type=float,
        default=0.2,
        help='Validation set ratio'
    )
    parser.add_argument(
        '--threshold_percentile',
        type=float,
        default=95,
        help='Percentile for reconstruction error threshold'
    )
    
    args = parser.parse_args()
    train_model(args)

if __name__ == "__main__":
    main()
