#!/usr/bin/env python3
"""
Create placeholder ML models for development and testing.
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import logging
import joblib

try:
    import tensorflow as tf
    print(f"TensorFlow version: {tf.__version__}")
except ImportError:
    print("TensorFlow not found. Installing...")
    os.system("pip install tensorflow")
    import tensorflow as tf

# Add src to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from src.models.autoencoder import BehavioralAutoencoder
from src.data_collection.preprocessor import BehavioralFeatureExtractor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_synthetic_data(n_samples: int = 1000, input_dim: int = 128) -> pd.DataFrame:
    """Generate synthetic behavioral data for testing.
    
    Args:
        n_samples: Number of samples to generate
        input_dim: Number of features
        
    Returns:
        DataFrame with synthetic data
    """
    # Generate random features
    data = np.random.randn(n_samples, input_dim).astype(np.float32)
    
    # Add some structure and correlations
    for i in range(input_dim // 4):
        data[:, i*4:(i+1)*4] = data[:, i*4:(i+1)*4] + np.random.randn(n_samples, 1)
        
    # Convert to DataFrame with feature names
    feature_names = [
        'typing_speed', 'mean_keyhold_time', 'std_keyhold_time',
        'mean_flight_time', 'std_flight_time', 'error_rate',
        'backspace_rate', 'special_key_rate'
    ]
    
    # Add digraph timing features
    digraphs = ['th', 'he', 'in', 'er', 'an', 're', 'on', 'at']
    feature_names.extend([f"{d}_time" for d in digraphs])
    
    # Add touch features
    touch_features = [
        'mean_velocity', 'std_velocity', 'max_velocity',
        'mean_acceleration', 'std_acceleration',
        'mean_pressure', 'std_pressure', 'min_pressure', 'max_pressure',
        'mean_touch_size', 'std_touch_size', 'min_touch_size', 'max_touch_size',
        'total_distance', 'path_efficiency', 'gesture_duration',
        'num_touch_points', 'touch_density'
    ]
    feature_names.extend(touch_features)
    
    # Fill remaining features with generic names
    while len(feature_names) < input_dim:
        feature_names.append(f"feature_{len(feature_names)}")
    
    df = pd.DataFrame(data, columns=feature_names[:input_dim])
    return df

def create_and_save_model(model_dir: str, input_dim: int = 128):
    """Create and save placeholder model and preprocessor.
    
    Args:
        model_dir: Directory to save model and preprocessor
        input_dim: Number of input features
    """
    logger.info(f"Creating model with input_dim={input_dim}")
    
    # Generate synthetic data
    X_train = generate_synthetic_data(n_samples=1000, input_dim=input_dim)
    X_val = generate_synthetic_data(n_samples=200, input_dim=input_dim)
    
    # Create and fit preprocessor
    preprocessor = BehavioralFeatureExtractor()
    X_train_scaled = preprocessor.fit_transform(X_train)
    X_val_scaled = preprocessor.transform(X_val)
    
    # Create and train model
    model = BehavioralAutoencoder(
        input_dim=input_dim,
        encoding_dims=[96, 64, 32],
        dropout_rate=0.2
    )
    
    history = model.fit(
        X_train_scaled,
        validation_data=(X_val_scaled, X_val_scaled),
        epochs=10,
        batch_size=32
    )
    
    # Compute threshold
    model.compute_reconstruction_threshold(X_train_scaled)
    
    # Save model and preprocessor
    os.makedirs(model_dir, exist_ok=True)
    
    logger.info(f"Saving model to {model_dir}")
    model.save(model_dir)
    
    logger.info(f"Saving preprocessor to {os.path.join(model_dir, 'preprocessor')}")
    preprocessor.save_preprocessor(os.path.join(model_dir, 'preprocessor'))
    
    # Save training history
    logger.info("Saving training history")
    joblib.dump(history, os.path.join(model_dir, 'training_history.pkl'))

def convert_to_tflite(model_dir: str):
    """Convert TensorFlow model to TFLite format for mobile deployment.
    
    Args:
        model_dir: Directory containing saved model
    """
    logger.info("Converting model to TFLite format")
    
    # Load model
    model = BehavioralAutoencoder(input_dim=128)  # Default size
    model.load(model_dir)
    
    # Convert to TFLite
    converter = tf.lite.TFLiteConverter.from_keras_model(model.autoencoder)
    converter.optimizations = [tf.lite.Optimize.DEFAULT]
    converter.target_spec.supported_types = [tf.float32]
    
    tflite_model = converter.convert()
    
    # Save TFLite model
    tflite_path = os.path.join(model_dir, 'model.tflite')
    with open(tflite_path, 'wb') as f:
        f.write(tflite_model)
    logger.info(f"Saved TFLite model to {tflite_path}")

def main():
    """Main function to create placeholder models."""
    # Set up model directories
    project_root = Path(__file__).parent.parent
    model_dir = project_root / 'models' / 'v1'
    mobile_model_dir = project_root / 'models' / 'mobile'
    
    os.makedirs(model_dir, exist_ok=True)
    os.makedirs(mobile_model_dir, exist_ok=True)
    
    try:
        # Create and save main model
        logger.info("Creating main model...")
        create_and_save_model(str(model_dir))
        
        # Convert to TFLite for mobile
        logger.info("Creating mobile model...")
        convert_to_tflite(str(model_dir))
        
        logger.info("Successfully created placeholder models!")
        
    except Exception as e:
        logger.error(f"Error creating models: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
