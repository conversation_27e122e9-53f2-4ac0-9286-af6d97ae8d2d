"""Pydantic schemas for behavioral data."""

from typing import Dict, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field

class BehavioralDataBase(BaseModel):
    """Base schema for behavioral data."""
    device_id: str
    session_id: Optional[str] = None
    data_type: str = Field(..., description="Type of behavioral data (raw, processed)")
    data: Dict = Field(..., description="Collected behavioral data")
    metadata: Optional[Dict] = None

class BehavioralDataCreate(BehavioralDataBase):
    """Schema for creating behavioral data."""
    pass

class BehavioralData(BehavioralDataBase):
    """Schema for behavioral data."""
    id: int
    user_id: str
    timestamp: datetime
    
    class Config:
        from_attributes = True

class BehavioralEventBase(BaseModel):
    """Base schema for behavioral events."""
    device_id: str
    session_id: Optional[str] = None
    event_type: str = Field(..., description="Type of behavioral event (typing, touch, etc.)")
    data: Dict = Field(..., description="Event-specific data")
    context: Optional[Dict] = None

class BehavioralEventCreate(BehavioralEventBase):
    """Schema for creating a behavioral event."""
    pass

class BehavioralEvent(BehavioralEventBase):
    """Schema for a behavioral event."""
    id: int
    user_id: str
    timestamp: datetime
    
    class Config:
        from_attributes = True

class BehavioralFeatureBase(BaseModel):
    """Base schema for behavioral features."""
    device_id: str
    session_id: Optional[str] = None
    feature_type: str = Field(..., description="Type of feature (typing, touch, combined)")
    features: Dict = Field(..., description="Extracted features")
    metadata: Optional[Dict] = None

class BehavioralFeatureCreate(BehavioralFeatureBase):
    """Schema for creating a behavioral feature."""
    pass

class BehavioralFeature(BehavioralFeatureBase):
    """Schema for a behavioral feature."""
    id: int
    user_id: str
    timestamp: datetime
    
    class Config:
        from_attributes = True

class BehavioralProfileBase(BaseModel):
    """Base schema for behavioral profiles."""
    device_id: str
    profile_type: str = Field(..., description="Type of behavioral profile")
    features: Dict = Field(..., description="Profile features")
    metadata: Optional[Dict] = None

class BehavioralProfileCreate(BehavioralProfileBase):
    """Schema for creating a behavioral profile."""
    pass

class BehavioralProfile(BehavioralProfileBase):
    """Schema for a behavioral profile."""
    id: int
    user_id: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class BehavioralProfile(BaseModel):
    """Schema for a user's behavioral profile on a specific device."""
    device_id: str
    events: List[BehavioralEvent]
    
    class Config:
        from_attributes = True

class BehavioralScore(BaseModel):
    """Schema for behavioral risk score."""
    user_id: str
    device_id: str
    score: float = Field(..., ge=0, le=1, description="Risk score between 0 and 1")
    confidence: float = Field(..., ge=0, le=1, description="Confidence in the score")
    factors: List[Dict] = Field(..., description="Contributing risk factors")
    timestamp: datetime
    
    class Config:
        from_attributes = True

class RiskAssessmentBase(BaseModel):
    """Base schema for risk assessments."""
    device_id: str
    session_id: Optional[str] = None
    risk_type: str = Field(..., description="Type of risk being assessed")
    data: Dict = Field(..., description="Assessment input data")

class RiskAssessmentCreate(RiskAssessmentBase):
    """Schema for creating a risk assessment."""
    pass

class RiskAssessment(RiskAssessmentBase):
    """Schema for a risk assessment."""
    id: int
    user_id: str
    score: float = Field(..., ge=0, le=1)
    confidence: float = Field(..., ge=0, le=1)
    factors: List[Dict]
    timestamp: datetime

    class Config:
        from_attributes = True

class RiskAssessmentResponse(RiskAssessment):
    """Schema for risk assessment response."""
    recommendations: Optional[List[Dict]] = None
    alerts: Optional[List[Dict]] = None
    
    class Config:
        from_attributes = True

class BehavioralTrigger(BaseModel):
    """Schema for triggering behavioral checks."""
    trigger_type: str = Field(..., description="Type of behavioral check to trigger")
    device_id: str
    context: Optional[Dict] = None
