"""API integration tests."""
from pathlib import Path
import pytest
from httpx import Async<PERSON><PERSON>
from typing import Dict, Any

from core.config import settings  # noqa: E402

@pytest.mark.asyncio
async def test_health_check(test_client: AsyncClient) -> None:
    """Test health check endpoint."""
    response = await test_client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data
    assert "components" in data

@pytest.mark.asyncio
async def test_auth_register_endpoint(test_client: AsyncClient) -> None:
    """Test user registration endpoint."""
    user_data = {
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "User",
        "password": "testpass123!@",
        "device_info": {
            "device_id": "test-device-1",
            "device_type": "mobile",
            "os": "android",
            "os_version": "12",
            "model": "Test Model",
            "brand": "Test Brand"
        }
    }
    
    response = await test_client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 201
    data = response.json()
    assert "id" in data
    assert data["email"] == user_data["email"]
    assert "password" not in data
    assert data["first_name"] == user_data["first_name"]
    assert data["last_name"] == user_data["last_name"]
    assert data["is_active"] is True

@pytest.mark.asyncio
async def test_auth_login_endpoint(test_client: AsyncClient) -> None:
    """Test user login endpoint."""
    # First register a test user
    user_data = {
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Two",
        "password": "testpass456!@",
        "device_info": {
            "device_id": "test-device-2",
            "device_type": "mobile",
            "os": "android",
            "os_version": "12",
            "model": "Test Model",
            "brand": "Test Brand"
        }
    }
    await test_client.post("/api/v1/auth/register", json=user_data)

    # Try to login
    login_data = {
        "username": user_data["email"],
        "password": user_data["password"]
    }
    response = await test_client.post(
        "/api/v1/auth/login",
        data=login_data,  # Using form data as that's what OAuth2 password flow expects
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"
    assert len(data["access_token"]) > 0

@pytest.mark.asyncio
async def test_protected_endpoints(test_client: AsyncClient, auth_token: str) -> None:
    """Test protected endpoints."""
    headers = {"Authorization": f"Bearer {auth_token}"}
    
    # Get user profile
    response = await test_client.get("/api/v1/users/me", headers=headers)
    assert response.status_code == 200
    data = response.json()
    assert "id" in data
    assert "email" in data

    # Try to access without token
    response = await test_client.get("/api/v1/users/me")
    assert response.status_code == 401

    # Try with invalid token
    headers["Authorization"] = "Bearer invalid.token.here"
    response = await test_client.get("/api/v1/users/me", headers=headers)
    assert response.status_code == 401
