# Application Settings
ENV=development
DEBUG=true
PROJECT_NAME=TrustChain-Auth
API_V1_STR=/api/v1

# Database
SQLALCHEMY_DATABASE_URI=postgresql://postgres:postgres@localhost:5432/trustchain_dev

# JWT
JWT_SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis
REDIS_URL=redis://localhost:6379/0

# Monitoring
SENTRY_DSN=
PROMETHEUS_MULTIPROC_DIR=/tmp

# Security
CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]
TRUSTED_HOSTS=["localhost","127.0.0.1"]

# ML Service
ML_SERVICE_URL=http://localhost:5000

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=json  # json or console
