import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:trustchain_auth/features/banking/domain/services/transaction_analytics_service.dart';
import 'package:trustchain_auth/features/banking/domain/services/behavioral_monitoring_service.dart';
import 'package:trustchain_auth/features/banking/domain/services/transaction_security_logger.dart';
import 'package:trustchain_auth/features/banking/domain/services/enhanced_transaction_security_service.dart';
import 'package:trustchain_auth/features/banking/domain/models/transaction.dart';

class MockTransactionAnalyticsService extends Mock 
    implements TransactionAnalyticsService {}

class MockBehavioralMonitoringService extends Mock 
    implements BehavioralMonitoringService {}

class MockTransactionSecurityLogger extends Mock 
    implements TransactionSecurityLogger {}

void main() {
  late EnhancedTransactionSecurityService securityService;
  late MockTransactionAnalyticsService mockAnalytics;
  late MockBehavioralMonitoringService mockBehavioral;
  late MockTransactionSecurityLogger mockLogger;

  setUp(() {
    mockAnalytics = MockTransactionAnalyticsService();
    mockBehavioral = MockBehavioralMonitoringService();
    mockLogger = MockTransactionSecurityLogger();

    securityService = EnhancedTransactionSecurityService(
      analyticsService: mockAnalytics,
      behavioralService: mockBehavioral,
      securityLogger: mockLogger,
    );
  });

  group('TransactionAnalyticsService Tests', () {
    test('analyzeTransactionRisk returns risk score between 0 and 1', () {
      final service = TransactionAnalyticsService();
      final transaction = Transaction(
        id: '1',
        userId: 'user1',
        amount: 1000.0,
        type: TransactionType.credit,
        description: 'Test',
        timestamp: DateTime.now(),
      );

      final riskScore = service.analyzeTransactionRisk(transaction);
      expect(riskScore >= 0 && riskScore <= 1, true);
    });

    test('updateTransactionPattern maintains pattern history', () {
      final service = TransactionAnalyticsService();
      const userId = 'user1';
      
      // Add 60 patterns (more than the 50 limit)
      for (var i = 0; i < 60; i++) {
        service.updateTransactionPattern(userId, 100.0);
      }

      final report = service.generateRiskReport(userId);
      expect(report['totalTransactions'], 50); // Should be limited to 50
      expect(report['averageAmount'], 100.0);
    });
  });

  group('BehavioralMonitoringService Tests', () {
    test('monitorTransactionBehavior emits scores', () async {
      final service = BehavioralMonitoringService();
      const userId = 'user1';

      final stream = service.monitorTransactionBehavior(userId);
      expect(stream, emits(isA<double>()));
    });

    test('generateBehavioralReport returns valid report', () {
      final service = BehavioralMonitoringService();
      const userId = 'user1';

      final report = service.generateBehavioralReport(userId);
      expect(report.containsKey('error'), true); // No data yet
      
      // Add some patterns and check again
      service.monitorTransactionBehavior(userId).listen((_) {});
      
      // Wait for patterns to be collected
      Future.delayed(const Duration(seconds: 6), () {
        final updatedReport = service.generateBehavioralReport(userId);
        expect(updatedReport.containsKey('patternCount'), true);
        expect(updatedReport.containsKey('averageTypingSpeed'), true);
      });
    });
  });

  group('TransactionSecurityLogger Tests', () {
    test('logs transaction attempts', () {
      final logger = TransactionSecurityLogger();
      final transaction = Transaction(
        id: '1',
        userId: 'user1',
        amount: 1000.0,
        type: TransactionType.credit,
        description: 'Test',
        timestamp: DateTime.now(),
      );

      logger.logTransactionAttempt(
        transaction, 
        0.5, 
        {'test': 'data'},
      );

      final report = logger.generateSecurityReport('user1');
      expect(report['totalTransactions'], 1);
      expect(report['averageRiskScore'], 0.5);
    });

    test('generates security alerts for suspicious patterns', () async {
      final logger = TransactionSecurityLogger();
      const userId = 'user1';

      // Create multiple high-risk transactions
      for (var i = 0; i < 3; i++) {
        final transaction = Transaction(
          id: 'test$i',
          userId: userId,
          amount: 5000.0,
          type: TransactionType.credit,
          description: 'High Risk Test',
          timestamp: DateTime.now(),
        );

        logger.logTransactionAttempt(transaction, 0.8, {});
      }

      expect(
        logger.alerts,
        emits(isA<SecurityAlert>()
          .having((a) => a.type, 'type', AlertType.multipleHighRisk)),
      );
    });
  });

  group('EnhancedTransactionSecurityService Integration Tests', () {
    test('evaluateTransaction combines all security aspects', () async {
      final transaction = Transaction(
        id: '1',
        userId: 'user1',
        amount: 1000.0,
        type: TransactionType.credit,
        description: 'Test',
        timestamp: DateTime.now(),
      );

      when(mockAnalytics.analyzeTransactionRisk(transaction))
          .thenReturn(0.3);

      when(mockBehavioral.monitorTransactionBehavior('user1'))
          .thenAnswer((_) => Stream.fromIterable([0.2]));

      final result = await securityService.evaluateTransaction(
        transaction,
        'user1',
      );

      expect(result.securityScore, isA<double>());
      expect(result.requiredVerificationLevel, isA<VerificationLevel>());
      verify(mockLogger.logTransactionAttempt(
        transaction,
        any,
        any,
      )).called(1);
    });
  });
}
