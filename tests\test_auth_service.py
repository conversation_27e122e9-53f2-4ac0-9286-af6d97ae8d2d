"""Test auth service."""
import os
import sys
from pathlib import Path
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from jose import jwt

# Make sure backend is in Python path
backend_path = str(Path(__file__).parent.parent / "backend")
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

from services.auth import AuthService
from schemas.user import UserCreate
from core.config import settings

@pytest.mark.asyncio
async def test_create_user(test_session: AsyncSession):
    """Test user creation."""
    auth_service = AuthService(test_session)
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpass123",
        first_name="Test",
        last_name="User"
    )
    user = await auth_service.create_user(user_data)
    assert user.email == "<EMAIL>"
    assert user.first_name == "Test"
    assert user.last_name == "User"
    assert user.is_active is True

@pytest.mark.asyncio
async def test_authenticate_user(test_session: AsyncSession):
    """Test user authentication."""
    auth_service = AuthService(test_session)
    # Create test user
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpass123",
        first_name="Auth",
        last_name="Test"
    )
    await auth_service.create_user(user_data)

    # Test authentication
    token = await auth_service.authenticate_user("<EMAIL>", "testpass123")
    assert token.token_type == "bearer"
    
    # Verify token
    payload = jwt.decode(
        token.access_token,
        settings.JWT_SECRET_KEY,
        algorithms=[settings.JWT_ALGORITHM]
    )
    assert "sub" in payload
    assert "exp" in payload
