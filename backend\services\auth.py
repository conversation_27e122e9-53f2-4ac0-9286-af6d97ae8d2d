"""Authentication service."""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple, List
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from fastapi import HTT<PERSON>Ex<PERSON>, status
from jose import jwt

from core.config import settings
from core.logging_config import logger
from db.models import User, <PERSON>ce, BehavioralEvent
from schemas.user import UserCreate, UserUpdate
from schemas.device import DeviceCreate
from schemas.auth import AuthEventCreate
from schemas.token import Token
from schemas.behavioral import BehavioralProfile

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AuthService:
    def __init__(self, db: AsyncSession):
        self.db = db

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a plain password against a hashed password."""
        return pwd_context.verify(plain_password, hashed_password)

    def get_password_hash(self, password: str) -> str:
        """Generate password hash."""
        return pwd_context.hash(password)

    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create a JWT access token."""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
        return encoded_jwt

    async def authenticate_user(self, email: str, password: str) -> Token:
        """Authenticate a user and return access token."""
        try:
            result = await self.db.execute(
                select(User).where(User.email == email)
            )
            user = result.scalar_one_or_none()
            
            if not user or not self.verify_password(password, user.hashed_password):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Incorrect email or password",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            access_token = self.create_access_token(
                data={"sub": user.email}
            )
            return Token(access_token=access_token, token_type="bearer")
        except HTTPException:
            raise
        except Exception as e:
            logger.error("authenticate_user_error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error during authentication"
            )

    async def create_user(self, user_data: UserCreate) -> User:
        """Create a new user."""
        # Check if user exists
        result = await self.db.execute(
            select(User).where(User.email == user_data.email)
        )
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Create user
        user = User(
            email=user_data.email,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            full_name=f"{user_data.first_name} {user_data.last_name}",
            hashed_password=self.get_password_hash(user_data.password)
        )
        
        try:
            self.db.add(user)
            await self.db.commit()  # Commit to get the user.id
            await self.db.refresh(user)

            # Create device if device info is provided
            if user_data.device_info:
                device = Device(
                    user_id=user.id,
                    device_id=user_data.device_info.get("device_id"),
                    device_type=user_data.device_info.get("device_type"),
                    device_info=user_data.device_info
                )
                self.db.add(device)
                await self.db.commit()

            return user
        except Exception as e:
            await self.db.rollback()
            logger.error("create_user_error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )

    async def register_device(self, user_id: int, device_data: DeviceCreate) -> Device:
        """Register a new device for a user."""
        try:
            # Check if device already exists
            result = await self.db.execute(
                select(Device).where(
                    Device.device_id == device_data.device_id,
                    Device.user_id == user_id
                )
            )
            if result.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Device already registered for this user"
                )

            device = Device(
                user_id=user_id,
                device_id=device_data.device_id,
                device_type=device_data.device_type,
                device_name=device_data.device_name
            )
            self.db.add(device)
            await self.db.commit()
            await self.db.refresh(device)
            return device
        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error("register_device_error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error registering device"
            )

    async def log_auth_event(self, user_id: int, event_data: AuthEventCreate) -> BehavioralEvent:
        """Log an authentication event."""
        try:
            event = BehavioralEvent(
                user_id=user_id,
                device_id=event_data.device_id,
                event_type=event_data.event_type,
                event_data=event_data.event_data,
                timestamp=event_data.timestamp or datetime.utcnow()
            )
            self.db.add(event)
            await self.db.commit()
            await self.db.refresh(event)
            return event
        except Exception as e:
            await self.db.rollback()
            logger.error("log_auth_event_error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error logging authentication event"
            )

    async def update_user(self, user_id: int, user_data: UserUpdate) -> User:
        """Update user information."""
        try:
            result = await self.db.execute(
                select(User).where(User.id == user_id)
            )
            user = result.scalar_one_or_none()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Update user fields
            for field, value in user_data.dict(exclude_unset=True).items():
                if value is not None:
                    setattr(user, field, value)

            await self.db.commit()
            await self.db.refresh(user)
            return user
        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error("update_user_error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating user"
            )

    async def delete_user(self, user_id: int) -> None:
        """Delete a user and all associated data."""
        try:
            result = await self.db.execute(
                select(User).where(User.id == user_id)
            )
            user = result.scalar_one_or_none()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            await self.db.delete(user)
            await self.db.commit()
        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error("delete_user_error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting user"
            )

    async def get_behavioral_profiles(self, user_id: int) -> List[BehavioralProfile]:
        """Get user's behavioral profiles."""
        try:
            # Get all behavioral events for the user
            result = await self.db.execute(
                select(BehavioralEvent).where(BehavioralEvent.user_id == user_id)
                .order_by(BehavioralEvent.timestamp.desc())
            )
            events = result.scalars().all()

            # Group events by device_id to create profiles
            profiles: Dict[str, List[BehavioralEvent]] = {}
            for event in events:
                if event.device_id not in profiles:
                    profiles[event.device_id] = []
                profiles[event.device_id].append(event)

            # Convert events to behavioral profiles
            return [
                BehavioralProfile(
                    device_id=device_id,
                    events=device_events
                )
                for device_id, device_events in profiles.items()
            ]
        except Exception as e:
            logger.error("get_behavioral_profiles_error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving behavioral profiles"
            )
