// Mocks generated by <PERSON><PERSON>to 5.4.5 from annotations
// in trustchain_auth/test/features/banking/domain/services/transaction_security_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:trustchain_auth/features/auth/domain/models/auth_models.dart'
    as _i2;
import 'package:trustchain_auth/features/auth/domain/services/continuous_auth_service.dart'
    as _i3;
import 'package:trustchain_auth/features/auth/domain/services/security_policy_service.dart'
    as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSecurityRequirement_0 extends _i1.SmartFake
    implements _i2.SecurityRequirement {
  _FakeSecurityRequirement_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ContinuousAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockContinuousAuthService extends _i1.Mock
    implements _i3.ContinuousAuthService {
  MockContinuousAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.AuthEvent> get authEvents => (super.noSuchMethod(
        Invocation.getter(#authEvents),
        returnValue: _i4.Stream<_i2.AuthEvent>.empty(),
      ) as _i4.Stream<_i2.AuthEvent>);

  @override
  _i4.Future<void> startMonitoring() => (super.noSuchMethod(
        Invocation.method(
          #startMonitoring,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  void stopMonitoring() => super.noSuchMethod(
        Invocation.method(
          #stopMonitoring,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void recordActivity() => super.noSuchMethod(
        Invocation.method(
          #recordActivity,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  double calculateRiskScore({
    required double? trustScore,
    required List<_i2.AuthEvent>? recentEvents,
    required String? currentLocation,
    required bool? isKnownDevice,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #calculateRiskScore,
          [],
          {
            #trustScore: trustScore,
            #recentEvents: recentEvents,
            #currentLocation: currentLocation,
            #isKnownDevice: isKnownDevice,
          },
        ),
        returnValue: 0.0,
      ) as double);

  @override
  _i2.RiskLevel getRiskLevel(double? riskScore) => (super.noSuchMethod(
        Invocation.method(
          #getRiskLevel,
          [riskScore],
        ),
        returnValue: _i2.RiskLevel.low,
      ) as _i2.RiskLevel);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [SecurityPolicyService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSecurityPolicyService extends _i1.Mock
    implements _i5.SecurityPolicyService {
  MockSecurityPolicyService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.SecurityPolicyEvent> get policyEvents => (super.noSuchMethod(
        Invocation.getter(#policyEvents),
        returnValue: _i4.Stream<_i2.SecurityPolicyEvent>.empty(),
      ) as _i4.Stream<_i2.SecurityPolicyEvent>);

  @override
  _i4.Future<void> startEnforcement() => (super.noSuchMethod(
        Invocation.method(
          #startEnforcement,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  void stopEnforcement() => super.noSuchMethod(
        Invocation.method(
          #stopEnforcement,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void recordFailedAttempt() => super.noSuchMethod(
        Invocation.method(
          #recordFailedAttempt,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void resetFailedAttempts() => super.noSuchMethod(
        Invocation.method(
          #resetFailedAttempts,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool isAccountLocked() => (super.noSuchMethod(
        Invocation.method(
          #isAccountLocked,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<_i2.SecurityRequirement> evaluateActionSecurity(
          _i2.SecurityAction? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #evaluateActionSecurity,
          [action],
        ),
        returnValue: _i4.Future<_i2.SecurityRequirement>.value(
            _FakeSecurityRequirement_0(
          this,
          Invocation.method(
            #evaluateActionSecurity,
            [action],
          ),
        )),
      ) as _i4.Future<_i2.SecurityRequirement>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
