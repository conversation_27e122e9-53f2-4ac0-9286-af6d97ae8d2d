"""
Contrastive Learning Model for Behavioral Biometrics User Verification
Uses contrastive learning to learn user-specific behavioral embeddings
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, Model
from typing import Dict, Any, List, Tuple, Optional
import logging

from .base_model import Base<PERSON>ehavioralModel, ModelTrainer, ModelPredictor

logger = logging.getLogger(__name__)


class ContrastiveLearningModel(BaseBehavioralModel):
    """Contrastive learning model for user verification"""
    
    def __init__(self, 
                 input_dim: int,
                 embedding_dim: int = 128,
                 hidden_dims: List[int] = [256, 128],
                 activation: str = 'relu',
                 dropout_rate: float = 0.3,
                 l2_reg: float = 1e-4,
                 margin: float = 1.0,
                 temperature: float = 0.1,
                 model_name: str = "ContrastiveLearningModel",
                 model_version: str = "1.0"):
        
        super().__init__(model_name, model_version)
        
        self.input_dim = input_dim
        self.embedding_dim = embedding_dim
        self.hidden_dims = hidden_dims
        self.activation = activation
        self.dropout_rate = dropout_rate
        self.l2_reg = l2_reg
        self.margin = margin
        self.temperature = temperature
        
        self.encoder = None
        self.contrastive_model = None
        self.verification_threshold = None
        
        self.model_params = {
            'input_dim': input_dim,
            'embedding_dim': embedding_dim,
            'hidden_dims': hidden_dims,
            'activation': activation,
            'dropout_rate': dropout_rate,
            'l2_reg': l2_reg,
            'margin': margin,
            'temperature': temperature
        }
    
    def _build_encoder(self) -> Model:
        """Build the encoder network"""
        inputs = keras.Input(shape=(self.input_dim,), name='encoder_input')
        x = inputs
        
        # Hidden layers
        for i, dim in enumerate(self.hidden_dims):
            x = layers.Dense(
                dim,
                activation=self.activation,
                kernel_regularizer=keras.regularizers.l2(self.l2_reg),
                name=f'encoder_dense_{i+1}'
            )(x)
            x = layers.BatchNormalization(name=f'encoder_bn_{i+1}')(x)
            x = layers.Dropout(self.dropout_rate, name=f'encoder_dropout_{i+1}')(x)
        
        # Embedding layer
        embeddings = layers.Dense(
            self.embedding_dim,
            activation=None,  # No activation for embeddings
            kernel_regularizer=keras.regularizers.l2(self.l2_reg),
            name='embeddings'
        )(x)
        
        # L2 normalize embeddings using Keras layer
        normalized_embeddings = layers.Lambda(
            lambda x: tf.nn.l2_normalize(x, axis=1),
            name='normalized_embeddings'
        )(embeddings)
        
        encoder = Model(inputs, normalized_embeddings, name='encoder')
        return encoder
    
    def _build_contrastive_model(self) -> Model:
        """Build the contrastive learning model"""
        # Anchor input
        anchor_input = keras.Input(shape=(self.input_dim,), name='anchor_input')
        
        # Positive/Negative input
        comparison_input = keras.Input(shape=(self.input_dim,), name='comparison_input')
        
        # Get embeddings using shared encoder
        anchor_embedding = self.encoder(anchor_input)
        comparison_embedding = self.encoder(comparison_input)
        
        # Calculate cosine similarity using Lambda layer
        similarity = layers.Lambda(
            lambda inputs: tf.reduce_sum(inputs[0] * inputs[1], axis=1),
            name='cosine_similarity'
        )([anchor_embedding, comparison_embedding])
        
        # Apply temperature scaling
        scaled_similarity = similarity / self.temperature
        
        model = Model(
            inputs=[anchor_input, comparison_input],
            outputs=scaled_similarity,
            name='contrastive_model'
        )
        
        return model
    
    def fit(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> 'ContrastiveLearningModel':
        """Build the contrastive learning model (actual training happens in trainer)"""
        # Build the encoder
        self.encoder = self._build_encoder()
        
        # Build the contrastive model
        self.contrastive_model = self._build_contrastive_model()
        
        # Compile the model
        self.contrastive_model.compile(
            optimizer='adam',
            loss=self._contrastive_loss,
            metrics=['accuracy']
        )
        
        # Store feature names if provided
        if hasattr(X, 'columns'):
            self.feature_names = list(X.columns)
        
        self.is_trained = True
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Get embeddings for input data"""
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")
        
        return self.encoder.predict(X)
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Predict verification probabilities (placeholder implementation)"""
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")
        
        # For contrastive learning, we need a reference embedding to compare against
        # This is a simplified implementation - in practice, you'd compare against
        # stored user embeddings
        embeddings = self.encoder.predict(X)
        
        # Placeholder: return random probabilities
        # In real implementation, compare against stored user embeddings
        return np.random.random(len(X))
    
    def verify_user(self, anchor_data: np.ndarray, test_data: np.ndarray) -> np.ndarray:
        """Verify if test_data belongs to the same user as anchor_data"""
        if not self.is_trained:
            raise ValueError("Model must be trained before verification")
        
        # Get similarities
        similarities = self.contrastive_model.predict([anchor_data, test_data])
        
        # Convert to verification probabilities
        verification_probs = tf.nn.sigmoid(similarities).numpy()
        
        return verification_probs
    
    def get_embeddings(self, X: np.ndarray) -> np.ndarray:
        """Get normalized embeddings for input data"""
        if not self.is_trained:
            raise ValueError("Model must be trained before getting embeddings")
        
        return self.encoder.predict(X)
    
    def set_verification_threshold(self, threshold: float):
        """Set the verification threshold"""
        self.verification_threshold = threshold
    
    def _contrastive_loss(self, y_true, y_pred):
        """Contrastive loss function"""
        # y_true: 1 for positive pairs (same user), 0 for negative pairs (different users)
        # y_pred: similarity scores
        
        # Positive loss: minimize distance for positive pairs
        positive_loss = y_true * tf.square(tf.maximum(0.0, self.margin - y_pred))
        
        # Negative loss: maximize distance for negative pairs
        negative_loss = (1 - y_true) * tf.square(tf.maximum(0.0, y_pred))
        
        return tf.reduce_mean(positive_loss + negative_loss)
    
    def _get_model_data(self) -> Dict[str, Any]:
        """Get model-specific data for saving"""
        model_data = {
            'verification_threshold': self.verification_threshold
        }
        
        if self.encoder is not None:
            model_data['encoder_weights'] = self.encoder.get_weights()
            model_data['encoder_config'] = self.encoder.get_config()
        
        if self.contrastive_model is not None:
            model_data['contrastive_weights'] = self.contrastive_model.get_weights()
            model_data['contrastive_config'] = self.contrastive_model.get_config()
        
        return model_data
    
    def _set_model_data(self, model_data: Dict[str, Any]):
        """Set model-specific data when loading"""
        self.verification_threshold = model_data.get('verification_threshold')
        
        if 'encoder_weights' in model_data and 'encoder_config' in model_data:
            # Rebuild the encoder
            self.encoder = self._build_encoder()
            self.encoder.set_weights(model_data['encoder_weights'])
        
        if 'contrastive_weights' in model_data and 'contrastive_config' in model_data:
            # Rebuild the contrastive model
            self.contrastive_model = self._build_contrastive_model()
            self.contrastive_model.compile(
                optimizer='adam',
                loss=self._contrastive_loss,
                metrics=['accuracy']
            )
            self.contrastive_model.set_weights(model_data['contrastive_weights'])


class ContrastiveTrainer(ModelTrainer):
    """Trainer for contrastive learning model"""

    def __init__(self, model: ContrastiveLearningModel):
        super().__init__(model)
        self.early_stopping = None
        self.model_checkpoint = None

    def train(self,
              X_train: np.ndarray,
              y_train: Optional[np.ndarray] = None,
              X_val: Optional[np.ndarray] = None,
              y_val: Optional[np.ndarray] = None,
              epochs: int = 100,
              batch_size: int = 32,
              validation_split: float = 0.2,
              early_stopping_patience: int = 10,
              reduce_lr_patience: int = 5,
              verbose: int = 1,
              **kwargs) -> Dict[str, Any]:
        """Train the contrastive learning model"""

        # Validate data
        self.validate_data(X_train, y_train)

        # Build the model if not already built
        if self.model.contrastive_model is None:
            self.model.fit(X_train, y_train)

        # Generate contrastive pairs
        anchor_data, comparison_data, labels = self._generate_contrastive_pairs(X_train, y_train)
        
        # Preprocess data
        anchor_processed = self.preprocess_data(anchor_data)
        comparison_processed = self.preprocess_data(comparison_data)

        # Setup callbacks
        callbacks = self._setup_callbacks(
            early_stopping_patience,
            reduce_lr_patience
        )

        # Train the model
        history = self.model.contrastive_model.fit(
            [anchor_processed, comparison_processed], 
            labels,
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split,
            callbacks=callbacks,
            verbose=verbose,
            **kwargs
        )

        # Calculate verification threshold
        val_similarities = self.model.contrastive_model.predict([anchor_processed, comparison_processed])
        threshold = np.percentile(val_similarities, 50)  # Median as threshold
        self.model.set_verification_threshold(threshold)

        # Store training metadata
        self.model.training_metadata = {
            'epochs_trained': len(history.history['loss']),
            'final_loss': float(history.history['loss'][-1]),
            'verification_threshold': float(threshold),
            'training_samples': len(X_train),
            'validation_samples': len(X_val) if X_val is not None else 0
        }

        return {
            'history': history.history,
            'verification_threshold': threshold,
            'training_metadata': self.model.training_metadata
        }

    def _generate_contrastive_pairs(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Generate positive and negative pairs for contrastive learning"""
        anchor_data = []
        comparison_data = []
        labels = []

        unique_users = np.unique(y)

        for user in unique_users:
            user_indices = np.where(y == user)[0]
            user_data = X[user_indices]

            # Generate positive pairs (same user)
            for i in range(len(user_data)):
                for j in range(i + 1, min(i + 5, len(user_data))):  # Limit pairs per user
                    anchor_data.append(user_data[i])
                    comparison_data.append(user_data[j])
                    labels.append(1)  # Positive pair

            # Generate negative pairs (different users)
            other_users = unique_users[unique_users != user]
            for other_user in other_users[:3]:  # Limit negative pairs
                other_indices = np.where(y == other_user)[0]
                if len(other_indices) > 0:
                    other_data = X[other_indices]

                    # Sample a few negative pairs
                    for i in range(min(3, len(user_data))):
                        for j in range(min(2, len(other_data))):
                            anchor_data.append(user_data[i])
                            comparison_data.append(other_data[j])
                            labels.append(0)  # Negative pair

        return np.array(anchor_data), np.array(comparison_data), np.array(labels)

    def _setup_callbacks(self, early_stopping_patience: int, reduce_lr_patience: int) -> List:
        """Setup training callbacks"""
        callbacks = []

        # Early stopping
        self.early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=early_stopping_patience,
            restore_best_weights=True,
            verbose=1
        )
        callbacks.append(self.early_stopping)

        # Reduce learning rate on plateau
        reduce_lr = keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=reduce_lr_patience,
            min_lr=1e-7,
            verbose=1
        )
        callbacks.append(reduce_lr)

        return callbacks

    def preprocess_data(self, X: np.ndarray) -> np.ndarray:
        """Preprocess data for contrastive learning"""
        # Normalize features to [0, 1] range
        X_processed = X.copy()

        # Handle missing values
        X_processed = np.nan_to_num(X_processed, nan=0.0, posinf=1.0, neginf=0.0)

        # Min-max normalization
        X_min = np.min(X_processed, axis=0)
        X_max = np.max(X_processed, axis=0)

        # Avoid division by zero
        X_range = X_max - X_min
        X_range[X_range == 0] = 1.0

        X_normalized = (X_processed - X_min) / X_range

        return X_normalized


class ContrastivePredictor(ModelPredictor):
    """Predictor for contrastive learning model"""

    def __init__(self, model: ContrastiveLearningModel):
        super().__init__(model)
        self.user_embeddings = {}  # Store user embeddings for verification

    def _convert_to_risk_scores(self, proba: np.ndarray) -> np.ndarray:
        """Convert verification probabilities to risk scores"""
        # For contrastive learning, lower verification probability = higher risk
        return 1.0 - proba

    def register_user(self, user_id: str, user_data: np.ndarray):
        """Register a user by storing their behavioral embeddings"""
        embeddings = self.model.get_embeddings(user_data)
        # Store the mean embedding as the user's profile
        self.user_embeddings[user_id] = np.mean(embeddings, axis=0)

    def verify_user(self, user_id: str, test_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Verify if test data belongs to the registered user"""
        if user_id not in self.user_embeddings:
            raise ValueError(f"User {user_id} not registered")

        # Get test embeddings
        test_embeddings = self.model.get_embeddings(test_data)
        user_embedding = self.user_embeddings[user_id]

        # Calculate similarities
        similarities = []
        for test_emb in test_embeddings:
            similarity = np.dot(test_emb, user_embedding)  # Cosine similarity (both are normalized)
            similarities.append(similarity)

        similarities = np.array(similarities)

        # Convert to verification probabilities
        verification_probs = tf.nn.sigmoid(similarities / self.model.temperature).numpy()

        # Calculate confidence based on similarity magnitude
        confidence = np.abs(similarities)

        return verification_probs, confidence

    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Predict risk scores with confidence intervals"""
        # For contrastive learning, we need a reference user
        # This is a simplified implementation
        embeddings = self.model.get_embeddings(X)

        # Calculate self-similarity as a proxy for confidence
        similarities = []
        for i, emb in enumerate(embeddings):
            if i > 0:
                sim = np.dot(emb, embeddings[i-1])
                similarities.append(sim)
            else:
                similarities.append(0.5)  # Default for first sample

        similarities = np.array(similarities)
        risk_scores = 1.0 - similarities  # Lower similarity = higher risk
        confidence = np.abs(similarities)

        return risk_scores, confidence

    def explain_prediction(self, X: np.ndarray, feature_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Provide detailed explanation for contrastive learning predictions"""
        embeddings = self.model.get_embeddings(X)

        explanation = {
            'embeddings': embeddings.tolist(),
            'embedding_dim': self.model.embedding_dim,
            'registered_users': list(self.user_embeddings.keys()),
            'model_type': 'contrastive_learning'
        }

        if feature_names:
            explanation['feature_names'] = feature_names

        return explanation

    def get_user_similarity_matrix(self) -> Dict[str, Any]:
        """Get similarity matrix between all registered users"""
        if not self.user_embeddings:
            return {'similarity_matrix': [], 'user_ids': []}

        user_ids = list(self.user_embeddings.keys())
        n_users = len(user_ids)
        similarity_matrix = np.zeros((n_users, n_users))

        for i, user1 in enumerate(user_ids):
            for j, user2 in enumerate(user_ids):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    emb1 = self.user_embeddings[user1]
                    emb2 = self.user_embeddings[user2]
                    similarity = np.dot(emb1, emb2)
                    similarity_matrix[i, j] = similarity

        return {
            'similarity_matrix': similarity_matrix.tolist(),
            'user_ids': user_ids
        }
