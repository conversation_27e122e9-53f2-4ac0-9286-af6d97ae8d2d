"""Test configuration."""
import os
import sys
from pathlib import Path
import pytest
from typing import AsyncGenerator, Any, Dict
import asyncio
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, AsyncEngine
from sqlalchemy.orm import sessionmaker
import pytest_asyncio
from httpx import AsyncClient
from jose import jwt

# Set test environment first
os.environ["ENV"] = "test"

# Add necessary paths to Python path
backend_path = str(Path(__file__).parent.parent / "backend")
ml_models_path = str(Path(__file__).parent.parent / "ml_models" / "src")
for path in [backend_path, ml_models_path]:
    if path not in sys.path:
        sys.path.insert(0, path)

# Import after setting paths
from core.config import settings  # noqa: E402
from db.models import Base  # noqa: E402
from db.session import get_async_session  # noqa: E402
from main import app  # noqa: E402

# Test database settings
test_db_path = Path(__file__).parent / "test.db"
test_db_url = f"sqlite+aiosqlite:///{test_db_path}"

@pytest.fixture(scope="session")
def event_loop() -> asyncio.AbstractEventLoop:
    """Create event loop for async tests."""
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop

@pytest_asyncio.fixture(scope="session")
async def test_engine() -> AsyncGenerator[AsyncEngine, None]:
    """Create test database engine."""
    engine = create_async_engine(
        test_db_url, 
        echo=False,
        connect_args={"check_same_thread": False}
    )
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
    
    try:
        yield engine
    finally:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        await engine.dispose()
        if test_db_path.exists():
            test_db_path.unlink()

@pytest_asyncio.fixture
async def test_session(test_engine: AsyncEngine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async_session = sessionmaker(
        bind=test_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False
    )

    async with async_session() as session:
        try:
            yield session
        finally:
            await session.rollback()
            
    await session.close()

@pytest_asyncio.fixture
async def test_client(test_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """Create test client with database session override."""
    async def override_get_session() -> AsyncGenerator[AsyncSession, None]:
        try:
            yield test_session
        finally:
            await test_session.rollback()

    app.dependency_overrides[get_async_session] = override_get_session
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        try:
            yield client
        finally:
            app.dependency_overrides.clear()

@pytest.fixture
def auth_token() -> str:
    """Create a test JWT auth token."""
    payload = {
        "sub": "<EMAIL>",  # Use email as subject
        "exp": datetime.utcnow() + timedelta(minutes=15),
        "type": "access"
    }
    return jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
