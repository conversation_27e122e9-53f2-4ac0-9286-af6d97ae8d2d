name: <PERSON><PERSON><PERSON><PERSON>-Auth CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # Python ML Models Testing
  ml-models-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run ML model tests
      run: |
        cd ml_models
        python -m pytest tests/ -v --cov=src --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./ml_models/coverage.xml
        flags: ml-models
        name: ml-models-coverage

  # Flutter App Testing
  flutter-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Cache Flutter dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.pub-cache
          mobile_app/.dart_tool
        key: ${{ runner.os }}-flutter-${{ hashFiles('mobile_app/pubspec.yaml') }}
        restore-keys: |
          ${{ runner.os }}-flutter-
    
    - name: Get Flutter dependencies
      run: |
        cd mobile_app
        flutter pub get
    
    - name: Analyze Flutter code
      run: |
        cd mobile_app
        flutter analyze
    
    - name: Run Flutter tests
      run: |
        cd mobile_app
        flutter test --coverage
    
    - name: Upload Flutter coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./mobile_app/coverage/lcov.info
        flags: flutter-app
        name: flutter-app-coverage

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and Deploy (on main branch)
  build-deploy:
    needs: [ml-models-test, flutter-test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Build Android APK
      run: |
        cd mobile_app
        flutter build apk --release
    
    - name: Build iOS (if on macOS runner)
      if: runner.os == 'macOS'
      run: |
        cd mobile_app
        flutter build ios --release --no-codesign
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: app-builds
        path: |
          mobile_app/build/app/outputs/flutter-apk/
          mobile_app/build/ios/iphoneos/
    
    - name: Create Release
      if: startsWith(github.ref, 'refs/tags/')
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false
