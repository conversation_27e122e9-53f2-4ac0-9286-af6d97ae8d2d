# 🧪 TrustChain-Auth Manual Testing Guide

## 🎯 Quick Start Testing

### **Prerequisites Verification**
```powershell
# 1. Verify Flutter is working
flutter doctor

# 2. Verify Python ML environment
.\venv\Scripts\Activate.ps1
python -c "import tensorflow; print('TensorFlow:', tensorflow.__version__)"

# 3. Verify Android environment
flutter devices
```

---

## 📱 Mobile App Manual Testing

### **1. Build and Run the App**
```powershell
cd mobile_app

# Clean build
flutter clean
flutter pub get

# Run on emulator/device
flutter run

# Or build APK
flutter build apk --release
```

### **2. Core App Testing Checklist**

#### **✅ App Launch & Navigation**
- [ ] App launches without crashes
- [ ] Splash screen displays correctly
- [ ] Navigation between screens works
- [ ] Back button functionality
- [ ] App doesn't freeze or hang

#### **✅ UI/UX Testing**
- [ ] All text is readable and properly sized
- [ ] Buttons respond to taps
- [ ] Scrolling works smoothly
- [ ] Colors and themes are consistent
- [ ] Icons display correctly
- [ ] Loading indicators work

#### **✅ Authentication Flow**
- [ ] Login screen displays
- [ ] Form validation works
- [ ] Error messages show appropriately
- [ ] Success navigation works
- [ ] Remember me functionality

#### **✅ Banking Dashboard**
- [ ] Account balance displays
- [ ] Transaction list loads
- [ ] Quick actions work
- [ ] Charts and graphs render
- [ ] Refresh functionality

#### **✅ Privacy Dashboard**
- [ ] Privacy score displays
- [ ] Data usage charts show
- [ ] Settings toggles work
- [ ] Export functionality
- [ ] Delete data options

### **3. Behavioral Data Collection Testing**

#### **✅ Touch Behavior**
- [ ] Tap different areas of screen
- [ ] Perform swipe gestures
- [ ] Test scroll patterns
- [ ] Try pinch-to-zoom
- [ ] Long press actions

#### **✅ Typing Dynamics**
- [ ] Type in search fields
- [ ] Enter text in forms
- [ ] Test different typing speeds
- [ ] Use backspace/delete
- [ ] Auto-complete interactions

#### **✅ Navigation Patterns**
- [ ] Move between screens
- [ ] Use bottom navigation
- [ ] Access drawer menu
- [ ] Deep link navigation
- [ ] Tab switching

#### **✅ Device Context**
- [ ] Rotate device orientation
- [ ] Move device around
- [ ] Test in different lighting
- [ ] Background/foreground transitions
- [ ] Network connectivity changes

### **4. Performance Testing**

#### **✅ App Performance**
- [ ] App starts within 3 seconds
- [ ] Smooth 60fps animations
- [ ] No memory leaks during use
- [ ] Battery usage is reasonable
- [ ] Network requests are efficient

#### **✅ ML Inference Performance**
- [ ] Real-time processing works
- [ ] No noticeable lag
- [ ] Background processing
- [ ] Model loading time
- [ ] Prediction accuracy

---

## 🧠 ML Models Manual Testing

### **1. Run Automated Tests**
```powershell
# Activate Python environment
.\venv\Scripts\Activate.ps1
cd ml_models

# Run all tests
python -m pytest tests/test_models.py -v

# Expected: 18/18 tests passing
```

### **2. Manual ML Model Testing**

#### **Create Test Script: `manual_ml_test.py`**
```python
import numpy as np
import sys
import os
sys.path.append('src')

from models import (
    BehavioralAutoencoder, AutoencoderTrainer,
    BehavioralOneClassSVM, OneClassSVMTrainer,
    ContrastiveLearningModel, ContrastiveTrainer
)

# Generate test data
np.random.seed(42)
X_train = np.random.random((100, 50))
X_test = np.random.random((20, 50))
y_train = np.random.randint(0, 5, 100)

print("🧠 Testing ML Models...")

# Test Autoencoder
print("\n1. Testing Autoencoder...")
autoencoder = BehavioralAutoencoder(input_dim=50)
autoencoder.fit(X_train)
trainer = AutoencoderTrainer(autoencoder)
trainer.train(X_train, epochs=2, verbose=0)
predictions = autoencoder.predict(X_test)
anomaly_scores = autoencoder.predict_proba(X_test)
print(f"✅ Autoencoder: Predictions shape {predictions.shape}, Anomaly scores range [{anomaly_scores.min():.3f}, {anomaly_scores.max():.3f}]")

# Test One-Class SVM
print("\n2. Testing One-Class SVM...")
svm = BehavioralOneClassSVM()
svm.fit(X_train)
svm_trainer = OneClassSVMTrainer(svm)
svm_trainer.train(X_train, hyperparameter_tuning=False, verbose=0)
svm_predictions = svm.predict(X_test)
svm_scores = svm.predict_proba(X_test)
print(f"✅ SVM: {np.sum(svm_predictions == 1)} normal, {np.sum(svm_predictions == -1)} anomalies")

# Test Contrastive Learning
print("\n3. Testing Contrastive Learning...")
contrastive = ContrastiveLearningModel(input_dim=50)
contrastive.fit(X_train)
embeddings = contrastive.get_embeddings(X_test)
verification = contrastive.verify_user(X_test[:5], X_test[5:10])
print(f"✅ Contrastive: Embeddings shape {embeddings.shape}, Verification scores range [{verification.min():.3f}, {verification.max():.3f}]")

print("\n🎉 All ML models working correctly!")
```

#### **Run Manual Test**
```powershell
python manual_ml_test.py
```

### **3. ML Model Performance Benchmarks**

#### **Expected Performance Metrics**
- **Autoencoder Training**: < 30 seconds for 100 epochs
- **SVM Training**: < 10 seconds for 1000 samples
- **Contrastive Learning**: < 60 seconds for 100 epochs
- **Inference Speed**: > 100 samples/second
- **Memory Usage**: < 500MB during training

---

## 🔒 Security & Privacy Testing

### **1. Privacy Features Testing**

#### **✅ Data Collection Controls**
- [ ] Toggle data collection settings
- [ ] Verify data stops being collected when disabled
- [ ] Check granular control options
- [ ] Test consent management

#### **✅ Data Export Testing**
- [ ] Export data in JSON format
- [ ] Export data in CSV format
- [ ] Export data in PDF format
- [ ] Verify exported data completeness
- [ ] Check file security

#### **✅ Data Deletion Testing**
- [ ] Delete specific data types
- [ ] Delete all data
- [ ] Verify data is actually removed
- [ ] Check deletion confirmation
- [ ] Test recovery prevention

### **2. Security Features Testing**

#### **✅ Authentication Security**
- [ ] Test failed login attempts
- [ ] Verify session timeouts
- [ ] Check auto-lock functionality
- [ ] Test biometric fallbacks
- [ ] Verify encryption

#### **✅ Behavioral Authentication**
- [ ] Test normal user patterns
- [ ] Simulate anomalous behavior
- [ ] Check risk score updates
- [ ] Verify challenge triggers
- [ ] Test panic gestures

---

## 🚨 Error Scenarios Testing

### **1. Network Issues**
- [ ] Test offline functionality
- [ ] Simulate poor connectivity
- [ ] Test connection recovery
- [ ] Verify data sync

### **2. Device Issues**
- [ ] Test low battery scenarios
- [ ] Simulate memory pressure
- [ ] Test device rotation
- [ ] Check background processing

### **3. Data Issues**
- [ ] Test with corrupted data
- [ ] Simulate storage full
- [ ] Test data migration
- [ ] Verify backup/restore

---

## 📊 Performance Benchmarks

### **Expected Results**
- **App Launch Time**: < 3 seconds
- **Screen Transitions**: < 500ms
- **ML Inference**: < 100ms per prediction
- **Data Export**: < 30 seconds for full dataset
- **Memory Usage**: < 200MB during normal use
- **Battery Impact**: < 5% per hour of active use

---

## ✅ Final Verification Checklist

### **Before Deployment**
- [ ] All automated tests pass
- [ ] Manual testing completed
- [ ] Performance benchmarks met
- [ ] Security features verified
- [ ] Privacy controls working
- [ ] No critical bugs found
- [ ] User experience is smooth
- [ ] Documentation is complete

### **Production Readiness**
- [ ] App builds successfully
- [ ] All features functional
- [ ] Error handling robust
- [ ] Performance optimized
- [ ] Security validated
- [ ] Privacy compliant
- [ ] User feedback positive
- [ ] Deployment scripts ready

---

## 🎯 Success Criteria

**The app is ready for production when:**
1. ✅ All tests pass (automated + manual)
2. ✅ Performance meets benchmarks
3. ✅ Security features work correctly
4. ✅ Privacy controls are functional
5. ✅ User experience is smooth
6. ✅ No critical bugs exist
7. ✅ Documentation is complete

**🎉 Congratulations! TrustChain-Auth is production-ready!**
