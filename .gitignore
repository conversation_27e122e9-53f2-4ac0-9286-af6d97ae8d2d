# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# PyCharm
.idea/

# VS Code
.vscode/

# Flutter/Dart
mobile_app/.dart_tool/
mobile_app/.flutter-plugins
mobile_app/.flutter-plugins-dependencies
mobile_app/.packages
mobile_app/.pub-cache/
mobile_app/.pub/
mobile_app/build/
mobile_app/ios/Flutter/Generated.xcconfig
mobile_app/ios/Runner/GeneratedPluginRegistrant.*

# Android
mobile_app/android/app/debug
mobile_app/android/app/profile
mobile_app/android/app/release

# iOS
mobile_app/ios/Flutter/flutter_export_environment.sh

# Machine Learning
ml_models/data/raw/
ml_models/data/processed/
ml_models/models/checkpoints/
*.h5
*.pkl
*.joblib
*.tflite

# Logs
*.log
logs/

# Environment variables
.env
.env.local
.env.development
.env.production

# Database
*.db
*.sqlite
*.sqlite3

# API Keys and Secrets
config/*/secrets.yaml
config/*/api_keys.json

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Documentation builds
docs/_build/
site/

# Backup files
*.bak
*.backup
