"""
One-Class SVM Model for Behavioral Biometrics Anomaly Detection
Uses One-Class Support Vector Machine to detect anomalous behavioral patterns
"""

import numpy as np
from sklearn.svm import OneClassSVM
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import classification_report
from typing import Dict, Any, List, Tuple, Optional, Union
import logging
import joblib

from .base_model import BaseBehavioralModel, ModelTrainer, ModelPredictor

logger = logging.getLogger(__name__)


class BehavioralOneClassSVM(BaseBehavioralModel):
    """One-Class SVM for behavioral biometrics anomaly detection"""
    
    def __init__(self, 
                 kernel: str = 'rbf',
                 gamma: Union[str, float] = 'scale',
                 nu: float = 0.05,
                 degree: int = 3,
                 coef0: float = 0.0,
                 tol: float = 1e-3,
                 cache_size: float = 200,
                 max_iter: int = -1,
                 model_name: str = "BehavioralOneClassSVM",
                 model_version: str = "1.0"):
        
        super().__init__(model_name, model_version)
        
        self.kernel = kernel
        self.gamma = gamma
        self.nu = nu
        self.degree = degree
        self.coef0 = coef0
        self.tol = tol
        self.cache_size = cache_size
        self.max_iter = max_iter
        
        self.svm_model = None
        self.scaler = None
        self.decision_threshold = None
        
        self.model_params = {
            'kernel': kernel,
            'gamma': gamma,
            'nu': nu,
            'degree': degree,
            'coef0': coef0,
            'tol': tol,
            'cache_size': cache_size,
            'max_iter': max_iter
        }
    
    def fit(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> 'BehavioralOneClassSVM':
        """Build the One-Class SVM model (actual training happens in trainer)"""
        # Initialize the SVM model
        self.svm_model = OneClassSVM(
            kernel=self.kernel,
            gamma=self.gamma,
            nu=self.nu,
            degree=self.degree,
            coef0=self.coef0,
            tol=self.tol,
            cache_size=self.cache_size,
            max_iter=self.max_iter
        )
        
        # Initialize scaler
        self.scaler = RobustScaler()  # More robust to outliers than StandardScaler
        
        # Store feature names if provided
        if hasattr(X, 'columns'):
            self.feature_names = list(X.columns)
        
        self.is_trained = True
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Predict anomalies (1 for normal, -1 for anomaly)"""
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")
        
        # Scale the data
        X_scaled = self.scaler.transform(X)
        
        # Get predictions from SVM
        predictions = self.svm_model.predict(X_scaled)
        
        return predictions
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Predict anomaly scores (distance from decision boundary)"""
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")
        
        # Scale the data
        X_scaled = self.scaler.transform(X)
        
        # Get decision function scores (distance from separating hyperplane)
        decision_scores = self.svm_model.decision_function(X_scaled)
        
        # Convert to probabilities (higher score = more normal, lower = more anomalous)
        # Normalize scores to [0, 1] range where 1 = anomaly, 0 = normal
        if self.decision_threshold is not None:
            # Use learned threshold
            anomaly_scores = np.where(decision_scores < self.decision_threshold, 
                                    1.0 - (decision_scores / self.decision_threshold), 
                                    0.0)
        else:
            # Use min-max normalization
            min_score = np.min(decision_scores)
            max_score = np.max(decision_scores)
            if max_score > min_score:
                # Invert scores so higher values indicate anomalies
                anomaly_scores = 1.0 - ((decision_scores - min_score) / (max_score - min_score))
            else:
                anomaly_scores = np.zeros_like(decision_scores)
        
        # Clip to [0, 1] range
        anomaly_scores = np.clip(anomaly_scores, 0, 1)
        
        return anomaly_scores
    
    def get_decision_scores(self, X: np.ndarray) -> np.ndarray:
        """Get raw decision function scores"""
        if not self.is_trained:
            raise ValueError("Model must be trained before getting decision scores")
        
        X_scaled = self.scaler.transform(X)
        return self.svm_model.decision_function(X_scaled)
    
    def set_decision_threshold(self, threshold: float):
        """Set the decision threshold for anomaly detection"""
        self.decision_threshold = threshold
    
    def get_support_vectors(self) -> np.ndarray:
        """Get support vectors from the trained model"""
        if not self.is_trained or self.svm_model is None:
            raise ValueError("Model must be trained before getting support vectors")
        
        return self.svm_model.support_vectors_
    
    def get_n_support_vectors(self) -> int:
        """Get number of support vectors"""
        if not self.is_trained or self.svm_model is None:
            raise ValueError("Model must be trained before getting support vector count")
        
        return len(self.svm_model.support_vectors_)
    
    def _get_model_data(self) -> Dict[str, Any]:
        """Get model-specific data for saving"""
        model_data = {
            'decision_threshold': self.decision_threshold
        }
        
        if self.svm_model is not None:
            model_data['svm_model'] = self.svm_model
        
        if self.scaler is not None:
            model_data['scaler'] = self.scaler
        
        return model_data
    
    def _set_model_data(self, model_data: Dict[str, Any]):
        """Set model-specific data when loading"""
        self.decision_threshold = model_data.get('decision_threshold')
        self.svm_model = model_data.get('svm_model')
        self.scaler = model_data.get('scaler')


class OneClassSVMTrainer(ModelTrainer):
    """Trainer for behavioral One-Class SVM"""

    def __init__(self, model: BehavioralOneClassSVM):
        super().__init__(model)
        self.best_params = None

    def train(self,
              X_train: np.ndarray,
              y_train: Optional[np.ndarray] = None,
              X_val: Optional[np.ndarray] = None,
              y_val: Optional[np.ndarray] = None,
              hyperparameter_tuning: bool = True,
              param_grid: Optional[Dict[str, List]] = None,
              cv_folds: int = 3,
              scoring: str = 'accuracy',
              verbose: int = 1,
              **kwargs) -> Dict[str, Any]:
        """Train the One-Class SVM"""

        # Validate data
        self.validate_data(X_train)

        # Build the model if not already built
        if self.model.svm_model is None:
            self.model.fit(X_train)

        # Preprocess data
        X_train_processed = self.preprocess_data(X_train)
        X_val_processed = None
        if X_val is not None:
            X_val_processed = self.preprocess_data(X_val)

        # Fit the scaler on training data
        self.model.scaler.fit(X_train_processed)
        X_train_scaled = self.model.scaler.transform(X_train_processed)

        if hyperparameter_tuning and param_grid is not None:
            # Perform hyperparameter tuning
            if verbose > 0:
                print("Performing hyperparameter tuning...")
            
            self._tune_hyperparameters(X_train_scaled, param_grid, cv_folds, scoring, verbose)
        
        # Train the final model
        if verbose > 0:
            print("Training One-Class SVM...")
        
        self.model.svm_model.fit(X_train_scaled)

        # Calculate decision threshold on training data
        train_scores = self.model.svm_model.decision_function(X_train_scaled)
        threshold = np.percentile(train_scores, 5)  # 5th percentile as threshold
        self.model.set_decision_threshold(threshold)

        # Store training metadata
        self.model.training_metadata = {
            'training_samples': len(X_train),
            'validation_samples': len(X_val) if X_val is not None else 0,
            'n_support_vectors': self.model.get_n_support_vectors(),
            'decision_threshold': float(threshold),
            'best_params': self.best_params
        }

        # Log training completion
        self.log_training_step({
            'step': 'training_complete',
            'n_support_vectors': self.model.get_n_support_vectors(),
            'threshold': float(threshold)
        })

        return {
            'decision_threshold': threshold,
            'n_support_vectors': self.model.get_n_support_vectors(),
            'best_params': self.best_params,
            'training_metadata': self.model.training_metadata
        }

    def _tune_hyperparameters(self, X_train_scaled: np.ndarray, param_grid: Dict[str, List],
                            cv_folds: int, scoring: str, verbose: int):
        """Perform hyperparameter tuning using GridSearchCV"""
        # Create a new SVM instance for tuning
        svm_for_tuning = OneClassSVM()

        # Perform grid search
        grid_search = GridSearchCV(
            svm_for_tuning,
            param_grid,
            cv=cv_folds,
            scoring=scoring,
            n_jobs=-1,
            verbose=verbose
        )

        grid_search.fit(X_train_scaled)

        # Update model with best parameters
        self.best_params = grid_search.best_params_
        self.model.svm_model.set_params(**self.best_params)

        if verbose > 0:
            print(f"Best parameters: {self.best_params}")
            print(f"Best score: {grid_search.best_score_:.4f}")

    def preprocess_data(self, X: np.ndarray) -> np.ndarray:
        """Preprocess data for SVM training"""
        # Handle missing values
        X_processed = np.nan_to_num(X, nan=0.0, posinf=1.0, neginf=0.0)

        return X_processed

    def get_default_param_grid(self) -> Dict[str, List]:
        """Get default parameter grid for hyperparameter tuning"""
        return {
            'kernel': ['rbf', 'poly', 'sigmoid'],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
            'nu': [0.01, 0.05, 0.1, 0.2, 0.5]
        }


class OneClassSVMPredictor(ModelPredictor):
    """Predictor for behavioral One-Class SVM"""

    def __init__(self, model: BehavioralOneClassSVM):
        super().__init__(model)

    def _convert_to_risk_scores(self, proba: np.ndarray) -> np.ndarray:
        """Convert SVM decision scores to risk scores"""
        # For One-Class SVM, proba already represents anomaly scores
        return proba

    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Predict risk scores with confidence intervals"""
        risk_scores = self.predict_risk_score(X)

        # Calculate confidence based on distance from decision boundary
        decision_scores = self.model.get_decision_scores(X)

        # Higher absolute decision score = higher confidence
        confidence = np.abs(decision_scores)

        # Normalize confidence to [0, 1] range
        if len(confidence) > 1:
            min_conf = np.min(confidence)
            max_conf = np.max(confidence)
            if max_conf > min_conf:
                confidence = (confidence - min_conf) / (max_conf - min_conf)
            else:
                confidence = np.ones_like(confidence)
        else:
            confidence = np.ones_like(confidence)

        return risk_scores, confidence

    def explain_prediction(self, X: np.ndarray, feature_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Provide detailed explanation for SVM predictions"""
        risk_scores = self.predict_risk_score(X)
        decision_scores = self.model.get_decision_scores(X)

        explanation = {
            'risk_scores': risk_scores.tolist(),
            'decision_scores': decision_scores.tolist(),
            'mean_risk': float(np.mean(risk_scores)),
            'max_risk': float(np.max(risk_scores)),
            'min_risk': float(np.min(risk_scores)),
            'decision_threshold': self.model.decision_threshold,
            'n_support_vectors': self.model.get_n_support_vectors()
        }

        if feature_names:
            explanation['feature_names'] = feature_names

        return explanation

    def detect_anomalies_batch(self,
                             X: np.ndarray,
                             threshold: Optional[float] = None,
                             return_scores: bool = False) -> Dict[str, Any]:
        """Detect anomalies in batch with detailed results"""
        if threshold is None:
            threshold = 0.5

        risk_scores = self.predict_risk_score(X)
        anomalies = risk_scores > threshold

        results = {
            'anomaly_count': int(np.sum(anomalies)),
            'anomaly_rate': float(np.mean(anomalies)),
            'anomaly_indices': np.where(anomalies)[0].tolist(),
            'threshold_used': threshold
        }

        if return_scores:
            results['risk_scores'] = risk_scores.tolist()
            results['anomaly_labels'] = anomalies.tolist()
            results['decision_scores'] = self.model.get_decision_scores(X).tolist()

        return results

    def get_feature_importance(self, X: np.ndarray, feature_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Get feature importance based on support vectors"""
        if not feature_names:
            feature_names = [f'feature_{i}' for i in range(X.shape[1])]

        # Get support vectors
        support_vectors = self.model.get_support_vectors()

        # Calculate feature importance as variance of support vectors
        feature_importance = np.var(support_vectors, axis=0)

        # Normalize to [0, 1] range
        if np.max(feature_importance) > 0:
            feature_importance = feature_importance / np.max(feature_importance)

        # Create feature importance dictionary
        importance_dict = {
            name: float(importance)
            for name, importance in zip(feature_names, feature_importance)
        }

        # Sort by importance
        sorted_features = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)

        return {
            'feature_importance': importance_dict,
            'top_features': sorted_features[:10],  # Top 10 features
            'n_support_vectors': len(support_vectors)
        }
