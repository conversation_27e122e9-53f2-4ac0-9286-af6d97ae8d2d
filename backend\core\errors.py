"""Custom exception handlers and error definitions for Trust<PERSON>hain-Auth."""

from fastapi import HTTPException, Request, status
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Any, Dict, Optional
from core.logging_config import logger

class TrustChainException(HTTPException):
    """Base exception for Trust<PERSON>hain-Auth application."""
    def __init__(
        self,
        status_code: int,
        detail: str,
        error_code: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> None:
        super().__init__(status_code=status_code, detail=detail)
        self.error_code = error_code
        self.headers = headers

class AuthenticationError(TrustChainException):
    """Raised when authentication fails."""
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code="AUTH_ERROR",
            headers={"WWW-Authenticate": "Bearer"}
        )

class AuthorizationError(TrustChainException):
    """Raised when user doesn't have permission."""
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code="FORBIDDEN"
        )

class ValidationError(TrustChainException):
    """Raised when input validation fails."""
    def __init__(self, detail: str = "Validation error"):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code="VALIDATION_ERROR"
        )

class NotFoundError(TrustChainException):
    """Raised when a resource is not found."""
    def __init__(self, detail: str = "Resource not found"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code="NOT_FOUND"
        )

async def trustchain_exception_handler(
    request: Request,
    exc: TrustChainException
) -> JSONResponse:
    """Handler for TrustChainException."""
    logger.error(
        "trustchain_error",
        error_code=exc.error_code,
        detail=exc.detail,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.detail,
                "status": exc.status_code,
                "path": request.url.path
            }
        },
        headers=exc.headers
    )

async def http_exception_handler(
    request: Request,
    exc: HTTPException
) -> JSONResponse:
    """Handler for HTTPException."""
    logger.error(
        "http_error",
        status_code=exc.status_code,
        detail=str(exc.detail),
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": "HTTP_ERROR",
                "message": exc.detail,
                "status": exc.status_code,
                "path": request.url.path
            }
        },
        headers=exc.headers
    )

async def validation_exception_handler(
    request: Request,
    exc: Any
) -> JSONResponse:
    """Handler for validation exceptions."""
    logger.error(
        "validation_error",
        errors=exc.errors(),
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "Input validation error",
                "status": status.HTTP_422_UNPROCESSABLE_ENTITY,
                "path": request.url.path,
                "details": exc.errors()
            }
        }
    )

async def python_exception_handler(
    request: Request,
    exc: Exception
) -> JSONResponse:
    """Handler for unhandled Python exceptions."""
    logger.exception(
        "unhandled_error",
        error=str(exc),
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": {
                "code": "INTERNAL_ERROR",
                "message": "Internal server error",
                "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
                "path": request.url.path
            }
        }
    )
