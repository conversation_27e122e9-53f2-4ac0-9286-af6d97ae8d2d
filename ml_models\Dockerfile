FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy ML service code
COPY src/ src/
COPY models/ models/

# Set environment variables
ENV PYTHONPATH=/app
ENV MODEL_PATH=/app/models
ENV TF_CPP_MIN_LOG_LEVEL=2

# Create non-root user
RUN useradd -m -u 1000 ml-server
RUN chown -R ml-server:ml-server /app
USER ml-server

# Expose port for FastAPI
EXPOSE 8000

# Run ML service with Uvicorn
CMD ["uvicorn", "src.server:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "2"]
