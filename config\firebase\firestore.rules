rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Authentication events - only authenticated users can write
    match /auth_events/{eventId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null 
        && request.auth.uid == resource.data.user_id;
    }
    
    // Security alerts - only authenticated users can write
    match /security_alerts/{alertId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null 
        && request.auth.uid == resource.data.user_id;
    }
    
    // Privacy settings - users can only access their own
    match /privacy_settings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Behavioral data - strictly user-specific
    match /behavioral_data/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public configuration data
    match /config/{document=**} {
      allow read: if true;
      allow write: if false; // Only admins can write config
    }
  }
}
