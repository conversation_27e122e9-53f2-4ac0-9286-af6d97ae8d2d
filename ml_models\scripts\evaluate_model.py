#!/usr/bin/env python3
"""Evaluate behavioral biometrics model performance."""

import os
import sys
import argparse
import logging
import json
from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.metrics import roc_curve, auc, precision_recall_curve
import matplotlib.pyplot as plt
from datetime import datetime

# Add src to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.autoencoder import BehavioralAutoencoder
from src.data_collection.preprocessor import BehavioralFeatureExtractor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_test_data(data_dir: str) -> tuple:
    """Load test data including normal and anomalous samples.
    
    Args:
        data_dir: Directory containing test data
        
    Returns:
        Tuple of (normal_features, anomaly_features)
    """
    normal_files = list(Path(data_dir).glob('normal/*.parquet'))
    anomaly_files = list(Path(data_dir).glob('anomaly/*.parquet'))
    
    if not normal_files or not anomaly_files:
        raise ValueError(f"Missing test data in {data_dir}")
        
    logger.info(f"Found {len(normal_files)} normal and {len(anomaly_files)} anomaly files")
    
    # Load and process data
    preprocessor = BehavioralFeatureExtractor()
    normal_features = []
    anomaly_features = []
    
    # Process normal data
    for file_path in normal_files:
        try:
            events_df = pd.read_parquet(file_path)
            
            if 'typing_events' in events_df.columns:
                features = preprocessor.extract_typing_features(
                    events_df['typing_events'].tolist()
                )
                normal_features.append(features)
            
            if 'touch_events' in events_df.columns:
                features = preprocessor.extract_touch_features(
                    events_df['touch_events'].tolist()
                )
                normal_features.append(features)
                
        except Exception as e:
            logger.warning(f"Error processing normal file {file_path}: {e}")
    
    # Process anomaly data
    for file_path in anomaly_files:
        try:
            events_df = pd.read_parquet(file_path)
            
            if 'typing_events' in events_df.columns:
                features = preprocessor.extract_typing_features(
                    events_df['typing_events'].tolist()
                )
                anomaly_features.append(features)
            
            if 'touch_events' in events_df.columns:
                features = preprocessor.extract_touch_features(
                    events_df['touch_events'].tolist()
                )
                anomaly_features.append(features)
                
        except Exception as e:
            logger.warning(f"Error processing anomaly file {file_path}: {e}")
    
    if not normal_features or not anomaly_features:
        raise ValueError("No valid features extracted from test data")
        
    return pd.concat(normal_features), pd.concat(anomaly_features)

def evaluate_model(args):
    """Evaluate model performance on test data.
    
    Args:
        args: Command line arguments
    """
    # Load model and preprocessor
    logger.info(f"Loading model from {args.model_dir}")
    model = BehavioralAutoencoder(input_dim=128)  # Will be overridden by loaded params
    model.load(args.model_dir)
    
    preprocessor = BehavioralFeatureExtractor()
    preprocessor.load_preprocessor(os.path.join(args.model_dir, 'preprocessor'))
    
    # Load and preprocess test data
    logger.info(f"Loading test data from {args.test_data}")
    normal_df, anomaly_df = load_test_data(args.test_data)
    
    X_normal = preprocessor.transform(normal_df)
    X_anomaly = preprocessor.transform(anomaly_df)
    
    # Get predictions
    normal_errors, normal_predictions = model.predict(X_normal)
    anomaly_errors, anomaly_predictions = model.predict(X_anomaly)
    
    # Combine for ROC and PR curves
    y_true = np.concatenate([
        np.zeros(len(X_normal)),
        np.ones(len(X_anomaly))
    ])
    
    errors = np.concatenate([normal_errors, anomaly_errors])
    
    # Compute ROC curve
    fpr, tpr, _ = roc_curve(y_true, errors)
    roc_auc = auc(fpr, tpr)
    
    # Compute PR curve
    precision, recall, _ = precision_recall_curve(y_true, errors)
    pr_auc = auc(recall, precision)
    
    # Compute metrics at current threshold
    predictions = np.concatenate([normal_predictions, anomaly_predictions])
    
    true_positives = np.sum((predictions == 1) & (y_true == 1))
    false_positives = np.sum((predictions == 1) & (y_true == 0))
    true_negatives = np.sum((predictions == 0) & (y_true == 0))
    false_negatives = np.sum((predictions == 0) & (y_true == 1))
    
    accuracy = (true_positives + true_negatives) / len(y_true)
    precision_score = true_positives / (true_positives + false_positives)
    recall_score = true_positives / (true_positives + false_negatives)
    f1_score = 2 * (precision_score * recall_score) / (precision_score + recall_score)
    
    # Save metrics
    metrics = {
        'timestamp': datetime.now().strftime('%Y%m%d_%H%M%S'),
        'roc_auc': float(roc_auc),
        'pr_auc': float(pr_auc),
        'accuracy': float(accuracy),
        'precision': float(precision_score),
        'recall': float(recall_score),
        'f1_score': float(f1_score),
        'n_normal_samples': len(X_normal),
        'n_anomaly_samples': len(X_anomaly),
        'normal_error_mean': float(np.mean(normal_errors)),
        'normal_error_std': float(np.std(normal_errors)),
        'anomaly_error_mean': float(np.mean(anomaly_errors)),
        'anomaly_error_std': float(np.std(anomaly_errors)),
        'threshold': float(model.reconstruction_threshold)
    }
    
    # Save evaluation results
    eval_dir = os.path.join(args.model_dir, 'evaluation')
    os.makedirs(eval_dir, exist_ok=True)
    
    with open(os.path.join(eval_dir, 'metrics.json'), 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # Plot ROC curve
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(fpr, tpr, color='darkorange', lw=2,
             label=f'ROC curve (AUC = {roc_auc:.2f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic')
    plt.legend(loc="lower right")
    
    # Plot PR curve
    plt.subplot(1, 2, 2)
    plt.plot(recall, precision, color='darkorange', lw=2,
             label=f'PR curve (AUC = {pr_auc:.2f})')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curve')
    plt.legend(loc="lower left")
    
    plt.tight_layout()
    plt.savefig(os.path.join(eval_dir, 'performance_curves.png'))
    
    logger.info(f"Evaluation results saved to {eval_dir}")
    logger.info(f"ROC AUC: {roc_auc:.3f}, PR AUC: {pr_auc:.3f}")
    logger.info(f"Accuracy: {accuracy:.3f}, F1 Score: {f1_score:.3f}")

def main():
    parser = argparse.ArgumentParser(description="Evaluate behavioral biometrics model")
    
    parser.add_argument(
        '--model_dir',
        type=str,
        required=True,
        help='Directory containing trained model'
    )
    parser.add_argument(
        '--test_data',
        type=str,
        required=True,
        help='Directory containing test data'
    )
    
    args = parser.parse_args()
    evaluate_model(args)

if __name__ == "__main__":
    main()
