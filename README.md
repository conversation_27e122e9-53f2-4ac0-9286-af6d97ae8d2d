# TrustChain-Auth

🔐 Objective:
With growing threats like session hijacking, credential theft, and account takeovers, static authentication (passwords, PINs, biometrics) is no longer enough. PersonaLock aims to deliver a passwordless, continuous authentication system that verifies identity through behavioral biometrics — ensuring real-time fraud detection, zero user friction, and maximum privacy.

By learning how a user naturally interacts with their device (typing, swiping, navigating), PersonaLock builds a dynamic digital fingerprint that silently protects sessions, detects intrusions, and adapts over time.

⚙ Implementation:
We are building a Flutter-based mobile banking prototype powered by on-device machine learning to ensure real-time performance and full privacy. The app continuously tracks:

Typing dynamics (speed, rhythm, dwell time)

Touch behavior (swipe velocity, tap force, gesture sequence)

App navigation flow (screen paths, interaction timing)

Geolocation and contextual cues (time, location changes)

Our ML stack uses Autoencoders, One-Class SVMs, and Contrastive Learning models — trained in Python and deployed with TensorFlow Lite to ensure low-latency, private inference.

Additionally, we’ve integrated Smart Pop-Up Challenges that trigger during moderate anomalies — asking privacy-safe, non-repetitive questions to verify identity without being intrusive.

🔑 Key Features:
✅ Passwordless Behavior-Based Login

✅ Continuous Real-Time Behavior Monitoring

✅ Smart Pop-Up Verification Questions (adaptive, privacy-safe)

✅ On-Device ML Inference with TFLite

✅ Adaptive Risk Scoring Engine

✅ Session Auto-Lock / Silent Logout on High Risk

✅ Panic Gestures (shake or volume long-press) for duress signaling

✅ Privacy Dashboard (view/manage/delete behavior data)

🌐 Applications:
Fintech & Banking: Continuous behavioral auth for high-risk transactions

E-commerce: Passive fraud detection during checkout sessions

Digital Identity: Seamless and persistent identity validation

Accessibility Use Cases: Personalized profiles for elderly and differently-abled users

🎯 Final Result:
Our working prototype demonstrates a secure, seamless, and invisible background authentication system that adapts to user behavior in real-time. It prevents unauthorized access even after login, minimizes false positives, and provides clear feedback when challenges arise.

Users can view and control their data through a built-in privacy dashboard, while silent panic gestures offer emergency protection under duress.

🔮 Post-Campaign Plans:
Federated Learning: Enable crowd-sourced learning without sharing raw data

Explainable AI (XAI): Inform users with feedback like “unusual typing rhythm”

Threat Modeling Enhancements: Detect session hijacking, social engineering

Multi-Device Support: Sync behavior profiles across user devices securely

Pilot Studies: Collaborate with banks like Canara to validate in real-world banking contexts

🧰 Tech Stack:
ML Training: Python, TensorFlow, scikit-learn, Keras

Mobile App: Flutter + Dart

Model Deployment: TensorFlow Lite (on-device)

Sensor Access: Flutter plugins (touch, location, device info)

Backend (Optional): Firebase / FastAPI for alerts & logging (no personal data storage)

Versioning: GitHub