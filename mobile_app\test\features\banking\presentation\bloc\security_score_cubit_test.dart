import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:trustchain_auth/features/auth/domain/services/continuous_auth_service.dart';
import 'package:trustchain_auth/features/auth/domain/models/security_score.dart';
import 'package:trustchain_auth/features/banking/presentation/bloc/security_score_cubit.dart';

@GenerateMocks([ContinuousAuthService])
void main() {
  late SecurityScoreCubit cubit;
  late MockContinuousAuthService mockContinuousAuthService;

  setUp(() {
    mockContinuousAuthService = MockContinuousAuthService();
    when(mockContinuousAuthService.securityScoreStream).thenAnswer(
      (_) => Stream.fromIterable([
        const SecurityScore(score: 0.8, riskLevel: RiskLevel.low, lastUpdated: null),
        const SecurityScore(score: 0.6, riskLevel: RiskLevel.medium, lastUpdated: null),
      ]),
    );
    cubit = SecurityScoreCubit(mockContinuousAuthService);
  });

  tearDown(() {
    cubit.close();
  });

  group('SecurityScoreCubit', () {
    test('initial state should have zero score and critical risk', () {
      expect(cubit.state.score, equals(0.0));
      expect(cubit.state.riskLevel, equals(RiskLevel.critical));
    });

    blocTest<SecurityScoreCubit, SecurityScore>(
      'should emit security scores from continuous auth service',
      build: () {
        return SecurityScoreCubit(mockContinuousAuthService);
      },
      expect: () => [
        const SecurityScore(score: 0.8, riskLevel: RiskLevel.low, lastUpdated: null),
        const SecurityScore(score: 0.6, riskLevel: RiskLevel.medium, lastUpdated: null),
      ],
    );

    test('refreshScore should update state with current security score', () async {
      // Arrange
      final newScore = const SecurityScore(
        score: 0.9,
        riskLevel: RiskLevel.low,
        lastUpdated: null,
      );
      when(mockContinuousAuthService.getCurrentSecurityScore())
          .thenAnswer((_) async => newScore);

      // Act
      await cubit.refreshScore();

      // Assert
      expect(cubit.state, equals(newScore));
    });

    test('currentScore should return current state', () {
      // Arrange
      final expectedScore = const SecurityScore(
        score: 0.0,
        riskLevel: RiskLevel.critical,
        lastUpdated: null,
      );

      // Act & Assert
      expect(cubit.currentScore, equals(expectedScore));
    });
  });
}
