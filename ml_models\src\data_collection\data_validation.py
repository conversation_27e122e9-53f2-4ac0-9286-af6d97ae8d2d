"""
Data Validation for Behavioral Biometrics
Ensures data quality and consistency
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime, timedelta
import logging

from .behavioral_data import (
    TypingDynamics, TouchBehavior, NavigationPattern, 
    ContextualData, BehavioralSession
)

logger = logging.getLogger(__name__)


class ValidationResult:
    """Result of data validation"""
    
    def __init__(self, is_valid: bool, errors: List[str] = None, warnings: List[str] = None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []
    
    def add_error(self, error: str):
        """Add validation error"""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str):
        """Add validation warning"""
        self.warnings.append(warning)
    
    def __str__(self):
        result = f"Valid: {self.is_valid}"
        if self.errors:
            result += f"\nErrors: {', '.join(self.errors)}"
        if self.warnings:
            result += f"\nWarnings: {', '.join(self.warnings)}"
        return result


class DataValidator:
    """Base validator class"""
    
    @staticmethod
    def validate_timestamp(timestamp: datetime, 
                         min_time: Optional[datetime] = None,
                         max_time: Optional[datetime] = None) -> ValidationResult:
        """Validate timestamp is reasonable"""
        result = ValidationResult(True)
        
        if not isinstance(timestamp, datetime):
            result.add_error("Timestamp must be datetime object")
            return result
        
        # Check if timestamp is in reasonable range
        now = datetime.now()
        if timestamp > now + timedelta(minutes=1):
            result.add_error("Timestamp is in the future")
        
        if timestamp < now - timedelta(days=365):
            result.add_warning("Timestamp is more than a year old")
        
        if min_time and timestamp < min_time:
            result.add_error(f"Timestamp {timestamp} is before minimum time {min_time}")
        
        if max_time and timestamp > max_time:
            result.add_error(f"Timestamp {timestamp} is after maximum time {max_time}")
        
        return result
    
    @staticmethod
    def validate_numeric_range(value: float, 
                             min_val: Optional[float] = None,
                             max_val: Optional[float] = None,
                             field_name: str = "value") -> ValidationResult:
        """Validate numeric value is in acceptable range"""
        result = ValidationResult(True)
        
        if not isinstance(value, (int, float)) or np.isnan(value) or np.isinf(value):
            result.add_error(f"{field_name} must be a valid number")
            return result
        
        if min_val is not None and value < min_val:
            result.add_error(f"{field_name} {value} is below minimum {min_val}")
        
        if max_val is not None and value > max_val:
            result.add_error(f"{field_name} {value} is above maximum {max_val}")
        
        return result


class TypingValidator(DataValidator):
    """Validator for typing dynamics data"""
    
    @staticmethod
    def validate_typing_event(event: TypingDynamics) -> ValidationResult:
        """Validate a single typing event"""
        result = ValidationResult(True)
        
        # Validate timestamp
        timestamp_result = DataValidator.validate_timestamp(event.timestamp)
        result.errors.extend(timestamp_result.errors)
        result.warnings.extend(timestamp_result.warnings)
        
        # Validate key timing
        if event.key_up_time <= event.key_down_time:
            result.add_error("Key up time must be after key down time")
        
        # Validate dwell time
        expected_dwell = event.key_up_time - event.key_down_time
        if abs(event.dwell_time - expected_dwell) > 0.001:  # Allow small floating point errors
            result.add_warning("Dwell time doesn't match key timing difference")
        
        # Validate dwell time range (typical human range: 50-500ms)
        dwell_result = DataValidator.validate_numeric_range(
            event.dwell_time, 0.01, 2.0, "dwell_time"
        )
        result.errors.extend(dwell_result.errors)
        
        # Validate flight time if present
        if event.flight_time is not None:
            flight_result = DataValidator.validate_numeric_range(
                event.flight_time, 0.0, 5.0, "flight_time"
            )
            result.errors.extend(flight_result.errors)
        
        # Validate pressure if present
        if event.pressure is not None:
            pressure_result = DataValidator.validate_numeric_range(
                event.pressure, 0.0, 1.0, "pressure"
            )
            result.errors.extend(pressure_result.errors)
        
        # Validate key code
        if not event.key_code or len(event.key_code) == 0:
            result.add_error("Key code cannot be empty")
        
        return result
    
    @staticmethod
    def validate_typing_sequence(events: List[TypingDynamics]) -> ValidationResult:
        """Validate a sequence of typing events"""
        result = ValidationResult(True)
        
        if len(events) < 2:
            result.add_warning("Typing sequence has fewer than 2 events")
            return result
        
        # Check temporal ordering
        for i in range(1, len(events)):
            if events[i].timestamp < events[i-1].timestamp:
                result.add_error(f"Events {i-1} and {i} are not in temporal order")
        
        # Check for reasonable inter-keystroke intervals
        intervals = []
        for i in range(1, len(events)):
            interval = (events[i].timestamp - events[i-1].timestamp).total_seconds()
            intervals.append(interval)
            
            if interval > 10.0:  # More than 10 seconds between keystrokes
                result.add_warning(f"Long interval ({interval:.2f}s) between keystrokes {i-1} and {i}")
        
        # Check for typing speed consistency
        if len(intervals) > 5:
            mean_interval = np.mean(intervals)
            std_interval = np.std(intervals)
            if std_interval > mean_interval * 2:
                result.add_warning("High variability in typing intervals")
        
        return result


class TouchValidator(DataValidator):
    """Validator for touch behavior data"""
    
    @staticmethod
    def validate_touch_event(event: TouchBehavior) -> ValidationResult:
        """Validate a single touch event"""
        result = ValidationResult(True)
        
        # Validate timestamp
        timestamp_result = DataValidator.validate_timestamp(event.timestamp)
        result.errors.extend(timestamp_result.errors)
        result.warnings.extend(timestamp_result.warnings)
        
        # Validate coordinates (assuming screen coordinates 0-1 normalized)
        x_result = DataValidator.validate_numeric_range(
            event.x_coordinate, 0.0, 1.0, "x_coordinate"
        )
        y_result = DataValidator.validate_numeric_range(
            event.y_coordinate, 0.0, 1.0, "y_coordinate"
        )
        result.errors.extend(x_result.errors + y_result.errors)
        
        # Validate pressure
        pressure_result = DataValidator.validate_numeric_range(
            event.pressure, 0.0, 1.0, "pressure"
        )
        result.errors.extend(pressure_result.errors)
        
        # Validate touch area
        area_result = DataValidator.validate_numeric_range(
            event.touch_area, 0.0, 1000.0, "touch_area"
        )
        result.errors.extend(area_result.errors)
        
        # Validate velocity if present
        if event.velocity is not None:
            if len(event.velocity) != 2:
                result.add_error("Velocity must be a 2-tuple (vx, vy)")
            else:
                for i, v in enumerate(event.velocity):
                    vel_result = DataValidator.validate_numeric_range(
                        v, -1000.0, 1000.0, f"velocity[{i}]"
                    )
                    result.errors.extend(vel_result.errors)
        
        return result


class NavigationValidator(DataValidator):
    """Validator for navigation pattern data"""
    
    @staticmethod
    def validate_navigation_event(event: NavigationPattern) -> ValidationResult:
        """Validate a single navigation event"""
        result = ValidationResult(True)
        
        # Validate timestamp
        timestamp_result = DataValidator.validate_timestamp(event.timestamp)
        result.errors.extend(timestamp_result.errors)
        result.warnings.extend(timestamp_result.warnings)
        
        # Validate screen names
        if not event.from_screen or len(event.from_screen.strip()) == 0:
            result.add_error("from_screen cannot be empty")
        
        if not event.to_screen or len(event.to_screen.strip()) == 0:
            result.add_error("to_screen cannot be empty")
        
        # Validate navigation time
        nav_time_result = DataValidator.validate_numeric_range(
            event.navigation_time, 0.0, 300.0, "navigation_time"
        )
        result.errors.extend(nav_time_result.errors)
        
        # Validate interaction sequence
        if not event.interaction_sequence:
            result.add_warning("Empty interaction sequence")
        
        return result


class BehavioralDataValidator:
    """Main validator for behavioral data"""
    
    def __init__(self):
        self.typing_validator = TypingValidator()
        self.touch_validator = TouchValidator()
        self.navigation_validator = NavigationValidator()
    
    def validate_session(self, session: BehavioralSession) -> ValidationResult:
        """Validate a complete behavioral session"""
        result = ValidationResult(True)
        
        # Validate session metadata
        if not session.session_id:
            result.add_error("Session ID cannot be empty")
        
        if not session.user_id:
            result.add_error("User ID cannot be empty")
        
        # Validate session timing
        if session.end_time and session.end_time <= session.start_time:
            result.add_error("Session end time must be after start time")
        
        # Validate individual event types
        for i, typing_event in enumerate(session.typing_data):
            typing_result = self.typing_validator.validate_typing_event(typing_event)
            if not typing_result.is_valid:
                result.add_error(f"Typing event {i}: {', '.join(typing_result.errors)}")
            result.warnings.extend([f"Typing event {i}: {w}" for w in typing_result.warnings])
        
        for i, touch_event in enumerate(session.touch_data):
            touch_result = self.touch_validator.validate_touch_event(touch_event)
            if not touch_result.is_valid:
                result.add_error(f"Touch event {i}: {', '.join(touch_result.errors)}")
            result.warnings.extend([f"Touch event {i}: {w}" for w in touch_result.warnings])
        
        for i, nav_event in enumerate(session.navigation_data):
            nav_result = self.navigation_validator.validate_navigation_event(nav_event)
            if not nav_result.is_valid:
                result.add_error(f"Navigation event {i}: {', '.join(nav_result.errors)}")
            result.warnings.extend([f"Navigation event {i}: {w}" for w in nav_result.warnings])
        
        # Validate typing sequence
        if len(session.typing_data) > 1:
            typing_seq_result = self.typing_validator.validate_typing_sequence(session.typing_data)
            result.errors.extend(typing_seq_result.errors)
            result.warnings.extend(typing_seq_result.warnings)
        
        # Session-level validations
        if session.total_events == 0:
            result.add_warning("Session has no behavioral events")
        
        if session.session_duration and session.session_duration < 1.0:
            result.add_warning("Very short session duration")
        
        return result
    
    def validate_feature_data(self, features_df: pd.DataFrame) -> ValidationResult:
        """Validate extracted feature data"""
        result = ValidationResult(True)
        
        if features_df.empty:
            result.add_error("Feature DataFrame is empty")
            return result
        
        # Check for missing values
        missing_cols = features_df.columns[features_df.isnull().any()].tolist()
        if missing_cols:
            result.add_warning(f"Columns with missing values: {missing_cols}")
        
        # Check for infinite values
        numeric_cols = features_df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if np.isinf(features_df[col]).any():
                result.add_error(f"Column {col} contains infinite values")
        
        # Check for constant features
        constant_cols = []
        for col in numeric_cols:
            if features_df[col].nunique() <= 1:
                constant_cols.append(col)
        
        if constant_cols:
            result.add_warning(f"Constant features detected: {constant_cols}")
        
        return result
