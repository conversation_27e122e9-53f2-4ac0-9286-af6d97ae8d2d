# TrustChain-Auth Development Setup Script for Windows
# PowerShell script to set up the complete development environment

Write-Host "🔐 TrustChain-Auth Development Environment Setup" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Function to run command with error handling
function Invoke-SafeCommand($command, $description) {
    Write-Host "📋 $description..." -ForegroundColor Yellow
    try {
        Invoke-Expression $command
        Write-Host "✅ $description completed successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ $description failed: $_" -ForegroundColor Red
        return $false
    }
}

# Check prerequisites
Write-Host "`n🔍 Checking prerequisites..." -ForegroundColor Blue

# Check Python
if (Test-Command python) {
    $pythonVersion = python --version
    Write-Host "✅ $pythonVersion detected" -ForegroundColor Green
} else {
    Write-Host "❌ Python is not installed. Please install Python 3.8+ first." -ForegroundColor Red
    exit 1
}

# Check Git
if (Test-Command git) {
    $gitVersion = git --version
    Write-Host "✅ $gitVersion detected" -ForegroundColor Green
} else {
    Write-Host "❌ Git is not installed. Please install Git first." -ForegroundColor Red
    exit 1
}

# Check Flutter
if (Test-Command flutter) {
    Write-Host "✅ Flutter is installed" -ForegroundColor Green
    $flutterAvailable = $true
} else {
    Write-Host "⚠️ Flutter is not installed. Will skip Flutter setup." -ForegroundColor Yellow
    Write-Host "   Install Flutter from: https://docs.flutter.dev/get-started/install" -ForegroundColor Yellow
    $flutterAvailable = $false
}

# Check Docker
if (Test-Command docker) {
    Write-Host "✅ Docker is installed" -ForegroundColor Green
    $dockerAvailable = $true
} else {
    Write-Host "⚠️ Docker is not installed. Will skip Docker setup." -ForegroundColor Yellow
    $dockerAvailable = $false
}

# Setup Python environment
Write-Host "`n🐍 Setting up Python environment..." -ForegroundColor Blue

if (-not (Test-Path "venv")) {
    Invoke-SafeCommand "python -m venv venv" "Creating Python virtual environment"
}

# Activate virtual environment and install dependencies
Write-Host "📦 Installing Python dependencies..." -ForegroundColor Yellow
Invoke-SafeCommand "venv\Scripts\pip install -r requirements.txt" "Installing ML dependencies"
Invoke-SafeCommand "venv\Scripts\pip install -r backend\requirements.txt" "Installing backend dependencies"

# Setup Flutter project
if ($flutterAvailable) {
    Write-Host "`n📱 Setting up Flutter project..." -ForegroundColor Blue
    
    if (-not (Test-Path "mobile_app\pubspec.yaml")) {
        Set-Location mobile_app
        Invoke-SafeCommand "flutter create . --org com.trustchain.auth" "Creating Flutter project"
        Set-Location ..
    }
    
    Set-Location mobile_app
    Invoke-SafeCommand "flutter pub get" "Getting Flutter dependencies"
    Invoke-SafeCommand "flutter doctor" "Running Flutter doctor"
    Set-Location ..
}

# Setup backend services
if ($dockerAvailable) {
    Write-Host "`n🔧 Setting up backend services..." -ForegroundColor Blue
    Invoke-SafeCommand "docker-compose up -d postgres redis" "Starting database services"
}

# Create configuration files
Write-Host "`n⚙️ Creating configuration files..." -ForegroundColor Blue

# Copy environment file
if (Test-Path "config\development\.env.example") {
    if (-not (Test-Path "config\development\.env")) {
        Copy-Item "config\development\.env.example" "config\development\.env"
        Write-Host "✅ Created development .env file" -ForegroundColor Green
    }
}

# Initialize Git repository if not already initialized
if (-not (Test-Path ".git")) {
    Invoke-SafeCommand "git init" "Initializing Git repository"
    Invoke-SafeCommand "git add ." "Adding files to Git"
    Invoke-SafeCommand "git commit -m 'Initial commit: TrustChain-Auth project setup'" "Creating initial commit"
}

# Setup pre-commit hooks
Write-Host "`n🔗 Setting up development tools..." -ForegroundColor Blue
Invoke-SafeCommand "venv\Scripts\pip install pre-commit" "Installing pre-commit"

# Create pre-commit config
$preCommitConfig = @"
repos:
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3
        files: ^(ml_models|backend)/.*\.py$
  
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        files: ^(ml_models|backend)/.*\.py$
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        files: ^(ml_models|backend)/.*\.py$
"@

$preCommitConfig | Out-File -FilePath ".pre-commit-config.yaml" -Encoding UTF8
Invoke-SafeCommand "venv\Scripts\pre-commit install" "Installing pre-commit hooks"

# Final summary
Write-Host "`n🎉 Setup completed!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Activate Python environment: venv\Scripts\activate" -ForegroundColor White
if ($flutterAvailable) {
    Write-Host "2. Start Flutter development: cd mobile_app && flutter run" -ForegroundColor White
}
if ($dockerAvailable) {
    Write-Host "3. Start backend services: docker-compose up -d" -ForegroundColor White
}
Write-Host "4. Open your IDE and start coding! 🚀" -ForegroundColor White

Write-Host "`n📚 Useful commands:" -ForegroundColor Cyan
Write-Host "- Run ML tests: cd ml_models && python -m pytest" -ForegroundColor White
Write-Host "- Run Flutter tests: cd mobile_app && flutter test" -ForegroundColor White
Write-Host "- Start backend API: cd backend && uvicorn main:app --reload" -ForegroundColor White
Write-Host "- View API docs: http://localhost:8000/docs" -ForegroundColor White
