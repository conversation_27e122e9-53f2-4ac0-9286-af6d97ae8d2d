name: trustchain_auth
description: "Trust<PERSON><PERSON><PERSON>-Auth: Behavioral Biometrics Banking App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.7.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Design
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9

  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  freezed_annotation: ^2.2.0
  json_annotation: ^4.8.1
  dartz: ^0.10.1  
  
  # Navigation
  go_router: ^12.1.3

  # Local Storage & Security
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^4.2.1
  crypto: ^3.0.6

  # Networking
  dio: ^5.8.0+1
  retrofit: ^4.0.3
  http: ^1.2.0

  # Sensors & Device Info
  sensors_plus: ^4.0.2
  device_info_plus: ^11.5.0
  permission_handler: ^11.1.0
  geolocator: ^10.1.0
  battery_plus: ^5.0.2
  local_auth: ^2.3.0

  # ML and TensorFlow Lite
  tflite_flutter: ^0.11.0

  # Utilities
  intl: ^0.19.0
  uuid: ^4.2.1
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2

  # Logging and Analytics
  logger: ^2.0.2+1

  # Charts and Visualization
  fl_chart: ^0.65.0
  timeago: ^3.7.1
  jwt_decoder: ^2.0.1
  root_check: ^0.0.2+1
  flutter_jailbreak_detection: ^1.10.0
  encrypt: ^5.0.3
  injectable: ^2.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting and Code Quality
  flutter_lints: ^5.0.0
  analyzer: ^6.11.0

  # Code Generation
  build_runner: ^2.4.7
  freezed: ^2.2.0
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.5

  # Testing
  mockito: ^5.4.4
  bloc_test: ^9.1.5

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  assets:
    - assets/icons/
    - assets/images/
    - assets/animations/
    - assets/ml_models/
