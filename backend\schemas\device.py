"""Device schemas."""

from pydantic import BaseModel
from typing import Optional, Dict
from datetime import datetime

class DeviceBase(BaseModel):
    device_id: str
    device_type: Optional[str] = None
    device_info: Optional[Dict] = None

class DeviceCreate(DeviceBase):
    pass

class DeviceUpdate(BaseModel):
    device_type: Optional[str] = None
    device_info: Optional[Dict] = None
    is_trusted: Optional[bool] = None

class DeviceResponse(DeviceBase):
    id: int
    user_id: int
    is_trusted: bool
    last_used: datetime
    created_at: datetime

    class Config:
        from_attributes = True

class Device(DeviceResponse):
    """Schema for a complete device, including all fields."""
    pass
