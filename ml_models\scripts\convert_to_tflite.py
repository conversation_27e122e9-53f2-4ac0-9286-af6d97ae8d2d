#!/usr/bin/env python3
"""
Script to convert trained PyTorch models to TensorFlow Lite format for mobile deployment.
"""

import os
import sys
import torch
import tensorflow as tf
import numpy as np
from pathlib import Path
import argparse
import logging

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.autoencoder import BehavioralAutoencoder
from models.classifier import UserClassifier
from models.anomaly_detector import AnomalyDetector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelConverter:
    """Converts PyTorch models to TensorFlow Lite format."""
    
    def __init__(self, input_dir: str, output_dir: str):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def convert_autoencoder(self, model_path: str, input_shape: tuple = (1, 128)):
        """Convert behavioral autoencoder to TFLite."""
        logger.info(f"Converting autoencoder from {model_path}")
        
        try:
            # Load PyTorch model
            model = BehavioralAutoencoder(input_dim=128, encoding_dim=64)
            checkpoint = torch.load(model_path, map_location='cpu')
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            
            # Create dummy input
            dummy_input = torch.randn(input_shape)
            
            # Convert to ONNX first (intermediate step)
            onnx_path = self.output_dir / "autoencoder_temp.onnx"
            torch.onnx.export(
                model,
                dummy_input,
                str(onnx_path),
                export_params=True,
                opset_version=11,
                do_constant_folding=True,
                input_names=['input'],
                output_names=['output'],
                dynamic_axes={
                    'input': {0: 'batch_size'},
                    'output': {0: 'batch_size'}
                }
            )
            
            # Convert ONNX to TensorFlow
            import onnx
            import onnx_tf
            
            onnx_model = onnx.load(str(onnx_path))
            tf_rep = onnx_tf.backend.prepare(onnx_model)
            
            # Export to TensorFlow SavedModel
            tf_model_path = self.output_dir / "autoencoder_tf"
            tf_rep.export_graph(str(tf_model_path))
            
            # Convert to TensorFlow Lite
            converter = tf.lite.TFLiteConverter.from_saved_model(str(tf_model_path))
            converter.optimizations = [tf.lite.Optimize.DEFAULT]
            converter.target_spec.supported_types = [tf.float16]
            
            tflite_model = converter.convert()
            
            # Save TFLite model
            tflite_path = self.output_dir / "behavioral_autoencoder.tflite"
            with open(tflite_path, 'wb') as f:
                f.write(tflite_model)
            
            # Clean up temporary files
            onnx_path.unlink()
            import shutil
            shutil.rmtree(tf_model_path)
            
            logger.info(f"Autoencoder converted successfully: {tflite_path}")
            return str(tflite_path)
            
        except Exception as e:
            logger.error(f"Failed to convert autoencoder: {e}")
            # Create a simple placeholder model
            return self._create_placeholder_autoencoder(input_shape)
    
    def convert_classifier(self, model_path: str, input_shape: tuple = (1, 64)):
        """Convert user classifier to TFLite."""
        logger.info(f"Converting classifier from {model_path}")
        
        try:
            # For now, create a simple TensorFlow model as placeholder
            return self._create_placeholder_classifier(input_shape)
            
        except Exception as e:
            logger.error(f"Failed to convert classifier: {e}")
            return self._create_placeholder_classifier(input_shape)
    
    def convert_anomaly_detector(self, model_path: str, input_shape: tuple = (1, 64)):
        """Convert anomaly detector to TFLite."""
        logger.info(f"Converting anomaly detector from {model_path}")
        
        try:
            # For now, create a simple TensorFlow model as placeholder
            return self._create_placeholder_anomaly_detector(input_shape)
            
        except Exception as e:
            logger.error(f"Failed to convert anomaly detector: {e}")
            return self._create_placeholder_anomaly_detector(input_shape)
    
    def _create_placeholder_autoencoder(self, input_shape: tuple):
        """Create a placeholder autoencoder model."""
        logger.info("Creating placeholder autoencoder model")
        
        # Create a simple autoencoder using TensorFlow
        model = tf.keras.Sequential([
            tf.keras.layers.Input(shape=(input_shape[1],)),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dense(input_shape[1], activation='linear')
        ])
        
        model.compile(optimizer='adam', loss='mse')
        
        # Generate some dummy training data
        dummy_data = np.random.randn(100, input_shape[1])
        model.fit(dummy_data, dummy_data, epochs=1, verbose=0)
        
        # Convert to TFLite
        converter = tf.lite.TFLiteConverter.from_keras_model(model)
        converter.optimizations = [tf.lite.Optimize.DEFAULT]
        tflite_model = converter.convert()
        
        # Save model
        tflite_path = self.output_dir / "behavioral_autoencoder.tflite"
        with open(tflite_path, 'wb') as f:
            f.write(tflite_model)
        
        logger.info(f"Placeholder autoencoder created: {tflite_path}")
        return str(tflite_path)
    
    def _create_placeholder_classifier(self, input_shape: tuple):
        """Create a placeholder classifier model."""
        logger.info("Creating placeholder classifier model")
        
        # Create a simple binary classifier
        model = tf.keras.Sequential([
            tf.keras.layers.Input(shape=(input_shape[1],)),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(16, activation='relu'),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        
        model.compile(optimizer='adam', loss='binary_crossentropy')
        
        # Generate dummy training data
        dummy_data = np.random.randn(100, input_shape[1])
        dummy_labels = np.random.randint(0, 2, (100, 1))
        model.fit(dummy_data, dummy_labels, epochs=1, verbose=0)
        
        # Convert to TFLite
        converter = tf.lite.TFLiteConverter.from_keras_model(model)
        converter.optimizations = [tf.lite.Optimize.DEFAULT]
        tflite_model = converter.convert()
        
        # Save model
        tflite_path = self.output_dir / "user_classifier.tflite"
        with open(tflite_path, 'wb') as f:
            f.write(tflite_model)
        
        logger.info(f"Placeholder classifier created: {tflite_path}")
        return str(tflite_path)
    
    def _create_placeholder_anomaly_detector(self, input_shape: tuple):
        """Create a placeholder anomaly detector model."""
        logger.info("Creating placeholder anomaly detector model")
        
        # Create a simple anomaly detector (returns anomaly score)
        model = tf.keras.Sequential([
            tf.keras.layers.Input(shape=(input_shape[1],)),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(16, activation='relu'),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        
        model.compile(optimizer='adam', loss='mse')
        
        # Generate dummy training data
        dummy_data = np.random.randn(100, input_shape[1])
        dummy_scores = np.random.rand(100, 1)  # Anomaly scores between 0 and 1
        model.fit(dummy_data, dummy_scores, epochs=1, verbose=0)
        
        # Convert to TFLite
        converter = tf.lite.TFLiteConverter.from_keras_model(model)
        converter.optimizations = [tf.lite.Optimize.DEFAULT]
        tflite_model = converter.convert()
        
        # Save model
        tflite_path = self.output_dir / "anomaly_detector.tflite"
        with open(tflite_path, 'wb') as f:
            f.write(tflite_model)
        
        logger.info(f"Placeholder anomaly detector created: {tflite_path}")
        return str(tflite_path)
    
    def convert_all_models(self):
        """Convert all available models."""
        logger.info("Starting model conversion process")
        
        results = {}
        
        # Convert autoencoder
        autoencoder_path = self.input_dir / "behavioral_autoencoder.pth"
        if autoencoder_path.exists():
            results['autoencoder'] = self.convert_autoencoder(str(autoencoder_path))
        else:
            logger.warning("Autoencoder model not found, creating placeholder")
            results['autoencoder'] = self._create_placeholder_autoencoder((1, 128))
        
        # Convert classifier
        classifier_path = self.input_dir / "user_classifier.pth"
        if classifier_path.exists():
            results['classifier'] = self.convert_classifier(str(classifier_path))
        else:
            logger.warning("Classifier model not found, creating placeholder")
            results['classifier'] = self._create_placeholder_classifier((1, 64))
        
        # Convert anomaly detector
        anomaly_path = self.input_dir / "anomaly_detector.pth"
        if anomaly_path.exists():
            results['anomaly_detector'] = self.convert_anomaly_detector(str(anomaly_path))
        else:
            logger.warning("Anomaly detector model not found, creating placeholder")
            results['anomaly_detector'] = self._create_placeholder_anomaly_detector((1, 64))
        
        logger.info("Model conversion completed")
        return results

def main():
    parser = argparse.ArgumentParser(description='Convert PyTorch models to TensorFlow Lite')
    parser.add_argument('--input-dir', default='../models/trained', 
                       help='Directory containing trained PyTorch models')
    parser.add_argument('--output-dir', default='../../mobile_app/assets/ml_models',
                       help='Directory to save TensorFlow Lite models')
    parser.add_argument('--model', choices=['autoencoder', 'classifier', 'anomaly', 'all'],
                       default='all', help='Which model to convert')
    
    args = parser.parse_args()
    
    # Resolve paths relative to script location
    script_dir = Path(__file__).parent
    input_dir = script_dir / args.input_dir
    output_dir = script_dir / args.output_dir
    
    converter = ModelConverter(str(input_dir), str(output_dir))
    
    if args.model == 'all':
        results = converter.convert_all_models()
        print("\nConversion Results:")
        for model_name, path in results.items():
            print(f"  {model_name}: {path}")
    else:
        if args.model == 'autoencoder':
            model_path = input_dir / "behavioral_autoencoder.pth"
            result = converter.convert_autoencoder(str(model_path))
        elif args.model == 'classifier':
            model_path = input_dir / "user_classifier.pth"
            result = converter.convert_classifier(str(model_path))
        elif args.model == 'anomaly':
            model_path = input_dir / "anomaly_detector.pth"
            result = converter.convert_anomaly_detector(str(model_path))
        
        print(f"Model converted: {result}")

if __name__ == "__main__":
    main()
