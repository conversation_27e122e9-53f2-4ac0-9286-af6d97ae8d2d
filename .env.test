PROJECT_NAME="<PERSON><PERSON><PERSON><PERSON>-Auth"
VERSION="1.0.0"
DEBUG=true
ENV="test"

# Security
SECRET_KEY="test-key-not-for-production-12345678901234567890123456789012"

# Database
POSTGRES_SERVER="localhost"
POSTGRES_USER="test_user"
POSTGRES_PASSWORD="test_password"
POSTGRES_DB="test_db"

# JWT
JWT_SECRET_KEY="jwt-test-key-12345678901234567890"
JWT_ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=5
REFRESH_TOKEN_EXPIRE_DAYS=1

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:8000", "http://localhost:3000"]

# Monitoring
PROMETHEUS_ENABLED=false
SENTRY_ENABLED=false

# Test Settings
USE_TEST_DB=true
TEST_DB_URL="sqlite+aiosqlite:///./test.db"
