# TrustChain-Auth Development Environment Configuration

# Application Settings
APP_NAME=Trust<PERSON>hain-Auth
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true

# ML Model Settings
MODEL_PATH=ml_models/models/
TFLITE_MODEL_PATH=ml_models/models/tflite/
DATA_PATH=ml_models/data/
TRAINING_BATCH_SIZE=32
INFERENCE_THRESHOLD=0.7
RISK_SCORE_THRESHOLD=0.8

# Behavioral Data Collection
COLLECT_TYPING_DYNAMICS=true
COLLECT_TOUCH_BEHAVIOR=true
COLLECT_NAVIGATION_PATTERNS=true
COLLECT_CONTEXTUAL_DATA=true
DATA_COLLECTION_INTERVAL=100  # milliseconds
MAX_DATA_RETENTION_DAYS=30

# Security Settings
ENCRYPTION_KEY_SIZE=256
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=3
PANIC_GESTURE_ENABLED=true

# Privacy Settings
PRIVACY_MODE=strict
DATA_ANONYMIZATION=true
LOCAL_STORAGE_ONLY=true
TELEMETRY_ENABLED=false

# Backend API (Optional)
API_BASE_URL=http://localhost:8000
API_TIMEOUT_SECONDS=30
ENABLE_LOGGING_API=false

# Firebase Configuration (Optional)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_API_KEY=your-api-key
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com

# Development Tools
ENABLE_FLUTTER_INSPECTOR=true
ENABLE_ML_DEBUGGING=true
LOG_LEVEL=debug
PERFORMANCE_MONITORING=true

# Testing
ENABLE_SYNTHETIC_DATA=true
MOCK_SENSORS=false
BYPASS_AUTHENTICATION=false
