"""FastAPI server for ML model serving."""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Optional, Union
import tensorflow as tf
import numpy as np
import joblib
import os
from datetime import datetime
import pandas as pd

from .models.autoencoder import BehavioralAutoencoder
from .data_collection.preprocessor import BehavioralFeatureExtractor

# Initialize FastAPI app
app = FastAPI(
    title="TrustChain-Auth ML Service",
    description="Machine Learning service for behavioral biometrics",
    version="1.0.0"
)

# Load ML models and preprocessors
model_path = os.getenv("MODEL_PATH", "./models")
model = None
preprocessor = None
model_version = "v1"

class BehavioralData(BaseModel):
    """Input behavioral data."""
    user_id: str
    typing_data: Optional[List[Dict]] = None
    touch_data: Optional[List[Dict]] = None
    device_info: Optional[Dict] = None

class RiskAssessment(BaseModel):
    """Risk assessment response."""
    risk_score: float
    anomaly_score: float
    confidence: float
    features_used: List[str]
    timestamp: datetime
    model_version: str

@app.on_event("startup")
async def load_model():
    """Load ML model and preprocessor on startup."""
    global model, preprocessor
    
    try:
        # Load preprocessor
        preprocessor = BehavioralFeatureExtractor()
        preprocessor.load_preprocessor(os.path.join(model_path, "preprocessor"))
        
        # Load model architecture
        model = BehavioralAutoencoder(
            input_dim=len(preprocessor.feature_columns),
            latent_dim=32
        )
        
        # Load model weights
        weights_path = os.path.join(model_path, "weights", "autoencoder.h5")
        model.load_weights(weights_path)
        
        # Load anomaly detection parameters
        params_path = os.path.join(model_path, "params")
        model.reconstruction_error_threshold = joblib.load(
            os.path.join(params_path, "reconstruction_threshold.pkl")
        )
        model.latent_features_mean = joblib.load(
            os.path.join(params_path, "latent_mean.pkl")
        )
        model.latent_features_std = joblib.load(
            os.path.join(params_path, "latent_std.pkl")
        )
    except Exception as e:
        print(f"Error loading model: {e}")
        model = None
        preprocessor = None

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    if model is None or preprocessor is None:
        raise HTTPException(
            status_code=503,
            detail="Model or preprocessor not loaded"
        )
    return {
        "status": "healthy",
        "model_version": model_version,
        "timestamp": datetime.utcnow()
    }

@app.post("/predict/risk", response_model=RiskAssessment)
async def predict_risk(
    data: BehavioralData,
    background_tasks: BackgroundTasks
):
    """Predict risk score from behavioral data."""
    if model is None or preprocessor is None:
        raise HTTPException(
            status_code=503,
            detail="Model or preprocessor not loaded"
        )
        
    try:
        # Extract features
        features = []
        if data.typing_data:
            typing_features = preprocessor.extract_typing_features(data.typing_data)
            features.append(typing_features)
        
        if data.touch_data:
            touch_features = preprocessor.extract_touch_features(data.touch_data)
            features.append(touch_features)
            
        if not features:
            raise ValueError("No behavioral data provided")
            
        # Combine and preprocess features
        X = pd.concat(features, axis=1)
        X = preprocessor.transform(X)
        
        # Get predictions
        reconstruction_errors, anomaly_predictions = model.predict(X)
        
        # Compute risk metrics
        avg_reconstruction_error = reconstruction_errors.mean()
        max_reconstruction_error = reconstruction_errors.max()
        anomaly_rate = anomaly_predictions.mean()
        
        # Calculate final risk score (0-1)
        risk_score = 1.0 - np.exp(-avg_reconstruction_error)
        
        # Calculate confidence based on amount and quality of data
        data_confidence = min(1.0, len(X) / 100)  # Scales with amount of data
        model_confidence = 1.0 - np.std(reconstruction_errors) / np.mean(reconstruction_errors)
        confidence = np.mean([data_confidence, model_confidence])
        
        # Add to background tasks
        background_tasks.add_task(
            update_user_profile,
            user_id=data.user_id,
            risk_score=risk_score,
            features=X
        )
        
        return RiskAssessment(
            risk_score=float(risk_score),
            anomaly_score=float(anomaly_rate),
            confidence=float(confidence),
            features_used=preprocessor.feature_columns,
            timestamp=datetime.utcnow(),
            model_version=model_version
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing request: {str(e)}"
        )

@app.post("/train")
async def train_model(background_tasks: BackgroundTasks):
    """Trigger model retraining on latest data."""
    try:
        background_tasks.add_task(retrain_model)
        return {
            "status": "Training job submitted",
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error starting training: {e}"
        )

async def update_user_profile(user_id: str, risk_score: float, features: np.ndarray):
    """Update user's behavioral profile."""
    try:
        # Get latent features
        latent_features = model.get_latent_features(features)
        
        # TODO: Store in database
        print(f"Updated profile for user {user_id}")
        
    except Exception as e:
        print(f"Error updating user profile: {e}")

async def retrain_model():
    """Retrain model on latest data."""
    global model, model_version
    
    try:
        # Load latest training data
        # TODO: Implement data loading from database
        
        # Train model
        X_train = None  # Load training data
        X_val = None  # Load validation data
        
        history = model.fit(
            X_train,
            validation_data=(X_val, X_val) if X_val is not None else None,
            epochs=100,
            batch_size=32
        )
        
        # Update threshold
        model.compute_reconstruction_threshold(X_train)
        
        # Save model
        new_version = f"v{int(model_version[1:]) + 1}"
        model_save_path = os.path.join(model_path, new_version)
        model.save(model_save_path)
        
        model_version = new_version
        print(f"Model retrained and saved as version {new_version}")
        
    except Exception as e:
        print(f"Error in model retraining: {e}")

@app.get("/model/info")
async def get_model_info():
    """Get current model information."""
    if model is None:
        raise HTTPException(
            status_code=503,
            detail="Model not loaded"
        )
    
    return {
        "version": model_version,
        "input_features": len(preprocessor.feature_columns),
        "feature_names": preprocessor.feature_columns,
        "last_trained": None,  # TODO: Add from metadata
        "total_parameters": model.autoencoder.count_params(),
        "architecture": {
            "input_dim": model.input_dim,
            "encoding_dims": model.encoding_dims,
            "latent_dim": model.encoding_dims[-1]
        }
    }
    
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8501)
